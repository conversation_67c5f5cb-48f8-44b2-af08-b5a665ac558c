@import url(~@fortawesome/fontawesome-free/css/all.css);
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('@/assets/fonts/pxiEyp8kv8JHgFVrJJfecg.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
:root {
  --primary: darkcyan;
  --secondary: #00b4d7;
  --dark: #343a40;
  --shadow: #00000033;
  --light: #f0f0f0;
  --transparent-white: #ffffff;
  --transparent-dark: #040f1b;
  --grey: #888;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: var(--transparent-white);
  border-radius: 10px;
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: var(--transparent-white);
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  cursor: pointer;
  background-color: var(--primary);
}
* {
  margin: 0;
  padding: 0;
  border: 0;
  touch-action: manipulation;
}
.leaflet-draw-actions a,
.nav-tabs .nav-link,
.nav-tabs .nav-link:hover,
.sidenav .clickedSideNav .icon-color,
.sidenav .clickedSideNav label,
.text-primary,
a {
  color: var(--primary) !important;
}
body {
  height: 100vh !important;
  overflow-x: hidden;
}
a {
  cursor: pointer;
}
.card-title,
.loc,
.trimmer {
  white-space: nowrap;
  text-overflow: ellipsis;
}
.btn:focus,
.custom-select:focus,
.headingContainer,
.inverterListContainer,
.sidenav {
  box-shadow: none !important;
}
.clickedTitle,
.sidenav .icon-color {
  font-size: 16px !important;
}
.primaryBg {
  background-color: var(--primary) !important;
}
.active {
  color: var(--primary) !important;
}
.b-sidebar-outer .shadow {
  box-shadow: 20px -23px 20px -8px #312d2d !important;
  margin-left: 2px;
}
.vdo-input {
  flex-grow: 1;
  margin: 0 10px;
  accent-color: darkcyan;
  cursor: pointer;
}
.transparentBg {
  background-color: var(--transparent-dark) !important;
  opacity: 0.5;
}
.card-text,
.card-title,
.loc,
.modal-content,
.trimmer {
  overflow: hidden;
}
.modal-body {
  overflow: auto;
}
#statusTableContainer .table td {
  vertical-align: middle !important;
}
.b-sidebar:not(.b-sidebar-right) {
  left: 70px !important;
  top: 0;
  right: auto;
  background: var(--transparent-white) !important;
  min-width: 220px;
  border-radius: 0;
  max-width: 270px !important;
  height: 100%;
}
.title {
  color: var(--light);
}
.project-select .dropdown-toggle {
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.project-select .dropdown-toggle::after {
  margin-left: auto;
}
.text-sm {
  font-size: 0.875rem;
}
.modal-header {
  padding: 0.5rem !important;
}
.leaflet-right .leaflet-control {
  margin-right: 7.5px;
}
.leaflet-bottom .leaflet-control {
  margin-bottom: 5px;
}
.leaflet-control-scale-line {
  box-sizing: border-box;
  background: var(--transparent-white);
  text-shadow: 1px 1px var(--white);
  font-size: 14px;
  font-weight: 600;
  border: solid 2px var(--primary) !important;
  border-radius: 5px;
}
.leaflet-control-scale {
  bottom: 5px !important;
  right: 4rem !important;
}
.leaflet-draw {
  position: fixed;
  right: 10px;
  bottom: 21.5rem;
}
.leaflet-draw-actions {
  right: 30px;
  left: auto !important;
}
.leaflet-draw-actions a {
  background: var(--transparent-white) !important;
  text-decoration: none !important;
}
.leaflet-bar,
.leaflet-control-toolbar {
  border: none !important;
}
.leaflet-toolbar-icon {
  border-radius: 50% !important;
}
.dropdown-menu > li > a:active,
.dropdown-menu > li > a:focus,
.dropdown-menu > li > button:active,
.dropdown-menu > li > button:focus,
.leaflet-toolbar-icon:hover {
  background-color: var(--primary) !important;
  color: var(--transparent-white) !important;
}
.leaflet-sbs-range {
  position: absolute;
  left: 0;
}
.custom-control {
  padding-left: 2rem !important;
}
.custom-control-label {
  font-size: 12px !important;
}
.leaflet-popup-content {
  margin: 15px;
  line-height: 1.5;
  font-size: 14px;
}
.leaflet-control-zoom {
  background: var(--dark);
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.btn-dark {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}
.dropdown-toggle {
  background-color: var(--dark) !important;
  color: var(--light) !important;
}
.indicator-line,
.step-icon {
  background: var(--primary) !important;
}
h4.sub_title {
  padding: 10px;
  margin: 0;
  color: var(--primary);
  font-size: 20px;
  color: var(--dark) !important;
  text-transform: uppercase !important;
  font-family: inherit !important;
}
.alert-dismissible,
.card-body {
  padding: 1rem !important;
}
.modal-title,
label {
  font-weight: 600;
}
.card-img-top {
  height: 170px;
}
.carousel-control-next,
.carousel-control-prev {
  width: 5% !important;
}
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(5px);
  z-index: 9999;
}
.logo-container img {
  position: absolute;
  height: 100px;
  border-radius: 20px;
}
.logo-container .spinner {
  width: 15rem !important;
  height: 15rem !important;
  animation-duration: 0.8s !important;
  border-width: 0.7rem;
  color: var(--primary);
}
.closeIcon:hover,
.page-item.active .page-link,
.sidenav,
.sidenav .icon-color,
.step-icon {
  color: var(--transparent-white) !important;
}
.active-link {
  background-color: var(--primary);
  padding: 10px;
  font-weight: 700;
  border-radius: 10px;
}
.navbar-nav .b-nav-item:hover {
  text-decoration: underline;
  cursor: pointer;
}
.nav-item,
.navbar-dark .navbar-nav .nav-link {
  font-size: 14px;
  color: var(--transparent-white) !important;
}
.navbar-nav .nav-link {
  padding: 5px 7.5px !important;
}
.b-sidebar > .b-sidebar-header {
  font-size: 1.2rem;
  justify-content: center;
  padding: 0.5rem;
  color: var(--primary);
  text-transform: uppercase;
  font-weight: 800;
}
.b-sidebar > .b-sidebar-header .close {
  margin-left: 1rem !important;
}
.b-table-sticky-header {
  max-height: calc(100vh - 185px) !important;
}
.b-table-sticky-header > .table.b-table > thead > tr > th {
  position: sticky !important;
}
.dropdown-menu.show {
  overflow-y: auto;
  max-height: 200px;
  min-width: 7.5rem !important;
}
.dropdown-menu > li > a,
.dropdown-menu > li > button {
  font-size: 14px !important;
  padding: 5px !important;
  text-align: center;
  color: var(--primary) !important;
}
.chat_converse,
.chat_header,
.sideBarList,
.timelineContainer {
  margin: 0 !important;
}
.pointer {
  cursor: pointer !important;
}
#app,
.leaflet-container,
body {
  font-family: Poppins, sans-serif !important;
  font-size: 14px !important;
}
.btn:not(.projectBtn),
.timelineContainer button {
  border-radius: 4px !important;
  -moz-border-radius: 4px !important;
  -webkit-border-radius: 4px !important;
  box-shadow: none !important;
  outline: 0 !important;
  font-size: 14px;
}
.card-title {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  width: 70%;
}
.card-btn,
.headingContainer,
.page-link,
.sideBarList,
.subItem1,
.subItem1.active,
.subItem1:hover,
b,
h4.sub_title,
strong {
  font-weight: 600 !important;
}
.loc {
  width: 40%;
  text-align: right;
}
.card-text {
  min-height: 40px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.projectButtonsContainer .btn {
  border: 0;
  background: var(--dark) !important;
  opacity: 1;
}
.chat_header {
  border-radius: 10px 10px 0 0 !important;
  padding: 10px !important;
  height: inherit !important;
}
.clickedHeader {
  border-radius: 5px !important;
  padding: 5px;
  cursor: pointer;
  background-color: var(--dark);
  color: var(--light);
  font-weight: 600;
}
.clickedDetails,
.sideBarList > div {
  background: var(--transparent-white);
}
.clickedDetails {
  min-width: 225px;
  padding: 0 20px;
}
.headingContainer {
  background: 0 0 !important;
  padding: 12px 25px !important;
  height: 54px !important;
  text-transform: uppercase;
  font-size: 18px;
  text-align: center !important;
  display: block !important;
}
.closeIcon,
.collapse,
.sideBarList > .collapse {
  padding: 10px !important;
}
.btn-primary,
.select-date .btn,
.sidenav {
  background: var(--dark) !important;
}
.timelineContainer {
  position: absolute;
  z-index: 999;
  display: block;
}
.back-btn,
.compare-page-btns {
  position: fixed;
  left: 10px;
  z-index: 999;
}
.select-date .btn {
  border-radius: 4px !important;
  font-weight: 600;
  font-size: 13px !important;
}
.sideBarList {
  margin: 10px !important;
}
.sideBarList > div {
  padding: 10px 20px !important;
  border-radius: 50px;
  transition: 0.1s;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}
.sideBarList > .collapse {
  margin-top: 5px;
  border-radius: 15px;
}
.inverterListContainer,
.summaryListContainer {
  padding: 0 15px !important;
}
.list-group .list-group-item {
  padding: 0;
  background: 0 0;
  border: none;
}
#sidenavLogo {
  background: #fefefe !important;
  padding-bottom: 6px;
  text-align: center;
}
.b-sidebar > .b-sidebar-body {
  background: #edf3f5 !important;
}
.inverterListContainer {
  max-height: calc(100vh - 100px) !important;
  margin: 0 0 15px !important;
}
.subItem1 {
  padding: 5px 0 !important;
}
.create-btn .btn,
.down_btn .btn {
  width: 40px;
  line-height: 40px;
  margin: 0;
  border-radius: 20px !important;
  padding: 0;
  height: 40px;
  background: var(--dark) !important;
  text-align: center;
}
.down_btn .btn {
  position: absolute;
  bottom: 5px;
  right: 15px;
}
.down_btn .download-drop {
  position: absolute;
  bottom: 0 !important;
  right: 0 !important;
}
.page-link {
  color: var(--primary) !important;
  font-size: 16px !important;
  border: 2px solid #6c757d !important;
}
.page-item.disabled .page-link {
  color: #6c757d !important;
}
.page-item.active .page-link {
  background-color: var(--dark) !important;
  border-color: var(--dark) !important;
}
.projectinfo-alert {
  width: 300px;
}
.compare-page-btns {
  width: 40px;
  top: 40px;
  display: block;
}
.back-btn {
  top: 10px;
  display: block;
}
.back-btn .btn,
.compare-page-btns .btn {
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
  border-radius: 50% !important;
  padding: 0 !important;
  text-align: center;
  line-height: 40px !important;
  background: var(--dark) !important;
}
.apexcharts-legend-text {
  font-size: 14px !important;
}
.page-count .custom-select {
  width: 150px;
  height: 30px;
  border: 1px solid #ddd !important;
  background-color: #f5f5f5 !important;
}
.page-count {
  position: absolute;
  top: -30px;
}
.page-count.alt {
  top: -50px;
}
.dates-filter {
  position: absolute;
  top: 10px;
  width: 100%;
  z-index: 999;
}
.dates-filter .date-select {
  width: 130px;
  font-size: 14px;
  margin: 0 auto !important;
  display: block;
  font-weight: 700;
  text-align: left;
}
.sideBarDetailsContainer {
  position: absolute;
  left: 280px;
  bottom: 5px;
  min-height: 70px;
  min-width: 200px !important;
  background-color: var(--light);
  transition: 1s ease-in;
}
.sidenavListItem {
  box-shadow: 0 3px 0 0 rgb(0 0 0 / 20%);
  background: var(--transparent-white);
  border-color: var(--dark);
  border-bottom: none;
  border-radius: 4px;
  padding: 5px;
  margin: 10px;
  text-transform: capitalize;
  cursor: pointer;
}
.custom-select,
.inner_flex span {
  background-color: #e9ecef;
  color: var(--dark);
}
.p-bar,
.p-bar .progress-bar {
  border-radius: 0 !important;
}
.sidenavListItem:hover {
  font-weight: 600;
  border-left: 5px solid var(--primary);
}
.upload-csv {
  right: 65px !important;
}
.leaflet-control-attribution {
  display: none;
}
.p-bar {
  margin: 15px !important;
}
.clickedTitle {
  padding: 10px 15px !important;
}
.inner_flex span {
  margin-right: 10px;
  border-radius: 4px !important;
  font-weight: 600;
  padding: 10px;
  text-align: center;
}
.custom-select {
  font-size: 14px;
  font-weight: 500;
}
.project-select {
  width: 150px !important;
  font-size: 14px;
  font-weight: 600;
  background-color: var(--transparent-white) !important;
  text-align: center !important;
}
.details {
  position: relative !important;
}
.chartContainer {
  width: 75%;
}
.chartLayout {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}
@media (max-width: 767px) {
  .sidenav {
    width: 55px !important;
  }
  .custom-select,
  .form-control {
    font-size: 16px;
    min-height: 44px;
  }
  .table {
    font-size: 12px;
  }
  .table td,
  .table th {
    padding: 4px 8px;
    vertical-align: middle;
  }
  .modal-dialog {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }
  .inner_login .card {
    margin: 8px;
    padding: 16px;
  }
  .login-with-btn {
    min-height: 44px;
    min-width: 44px;
    margin: 4px;
  }
  .pagination {
    justify-content: center;
    flex-wrap: wrap;
  }
  .page-link {
    padding: 6px 10px;
    font-size: 14px;
    min-height: 36px;
  }
  .b-sidebar:not(.b-sidebar-right) {
    left: 50px !important;
    top: 48px;
    right: auto;
    background: var(--transparent-white) !important;
    min-width: 203px;
    max-width: 196px !important;
    height: 40%;
  }
  .sideBarDetailsContainer {
    position: absolute;
    left: 9px;
    bottom: -136px;
    min-height: 70px;
    min-width: 200px !important;
    background-color: var(--light);
    transition: 1s ease-in;
  }
  .chartContainer {
    width: 100% !important;
    overflow-x: auto;
  }
  .width-set {
    width: fit-content !important;
  }
  .form-group {
    margin-bottom: 16px;
  }
  .form-control {
    min-height: 44px;
  }
  textarea.form-control {
    min-height: 80px;
  }
  :deep(.modal-body) {
    padding: 12px;
  }
  .custom-select {
    min-height: 44px;
  }
}
@media (min-width: 768px) {
  .chartContainer {
    width: 100% !important;
    overflow-x: auto;
  }
}
.carousel-control-next,
.carousel-control-prev {
  background-color: var(--dark);
}
.inner_login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  padding: 0.5rem;
}
.max-width-500 {
  max-width: 500px;
}
.font-size-24 {
  font-size: 24px;
}
.max-width-450 {
  max-width: 450px;
}
.max-width-300 {
  max-width: 300px;
}
.max-width-200 {
  max-width: 200px;
}
.max-width-150 {
  max-width: 150px;
}
.min-width-150 {
  min-width: 150px;
}
.min-width-200 {
  min-width: 200px;
}
.min-width-250 {
  min-width: 250px;
}
.min-width-300 {
  min-width: 300px;
}
.min-width-350 {
  min-width: 350px;
}
.max-height-200 {
  max-height: 200px;
}
.min-height-44 {
  min-height: 44px;
}
.min-height-36 {
  min-height: 36px;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.color-unset {
  color: unset !important;
}
#print-template {
  visibility: hidden;
  position: fixed;
}
@media print {
  #print-map,
  #print-template {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
  }
  body * {
    visibility: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  #print-template,
  #print-template * {
    visibility: visible !important;
  }
  #print-template {
    display: flex !important;
    flex-direction: column !important;
    padding: 5px !important;
    box-sizing: border-box !important;
    background-color: var(--transparent-white) !important;
  }
  .print-header {
    flex: 0 0 auto !important;
    margin-bottom: 10px !important;
    border-bottom: 2px solid #333 !important;
    padding-bottom: 10px !important;
  }
  .header-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }
  .print-map-container {
    flex: 1 1 auto !important;
    position: relative !important;
    width: 100% !important;
    margin: 0 !important;
    border: 1px solid #ccc !important;
    page-break-inside: avoid !important;
    overflow: hidden !important;
  }
  #print-map {
    background: #f9f9f9 !important;
  }
  .print-footer {
    flex: 0 0 auto !important;
    margin-top: 10px !important;
    padding-top: 10px !important;
    border-top: 2px solid #333 !important;
    text-align: center !important;
  }
  .leaflet-control,
  .leaflet-control-container,
  .leaflet-popup {
    display: none !important;
  }
  @page {
    size: A4 landscape !important;
    margin: 0.5cm;
  }
}
