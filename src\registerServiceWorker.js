import { register } from 'register-service-worker';

if (process.env.NODE_ENV === 'production') {
  register(`${process.env.BASE_URL}service-worker.js`, {
    ready() {
      if (process.env.NODE_ENV === 'development') {
        console.log('App is being served from cache by a service worker.');
      }
    },
    registered() {
      if (process.env.NODE_ENV === 'development') {
        console.log('Service worker has been registered.');
      }
    },
    cached() {
      if (process.env.NODE_ENV === 'development') {
        console.log('Content has been cached for offline use.');
      }
    },
    updatefound() {
      if (process.env.NODE_ENV === 'development') {
        console.log('New content is downloading.');
      }
    },
    updated() {
      if (process.env.NODE_ENV === 'development') {
        console.log('New content is available; please refresh.');
      }
    },
    offline() {
      if (process.env.NODE_ENV === 'development') {
        console.log('No internet connection found. App is running in offline mode.');
      }
    },
    error(error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error during service worker registration:', error);
      }
    },
  });
}
