<template>
  <div>
    <div class="sidenavListContainer">
      <div
        class="d-flex justify-content-between sidenavListItem"
        :class="{
          clickedHighlighter: clickedItemKey === currentDate + measureItem.label,
        }"
        @click="measureItemClicked(measureItem)"
        v-for="(measureItem, key, index) in measuresArray"
        :key="index">
        <div>
          <span class="ml-2">{{ measureItem.label }}</span>
        </div>
        <div>
          <span class="measureTypeIcon text-primary mr-2"
            ><i :class="measureIconClasses[measureItem.type]"></i
          ></span>
        </div>
      </div>

      <div class="text-center create-btn">
        <b-button
          pill
          class="primaryBg mt-2 border-0"
          @click="createNewMeasure()"
          v-b-tooltip.hover.bottom
          title="Create New Measurement"
          ><i class="fa fa-plus"></i
        ></b-button>
      </div>
    </div>

    <div v-if="clickedDetailsVisible" class="d-flex flex-column sideBarDetailsContainer">
      <div class="d-flex justify-content-between align-items-center bg-dark rounded-top">
        <b class="ml-2 text-light">{{ clickedDetails.label }}</b>
        <div role="button" class="mr-2 text-light closeIcon">
          <i class="icon fa fa-times" aria-controls="sidebar" @click="closeDetails()"></i>
        </div>
      </div>
      <div class="sideBarDetails d-flex align-items-center rounded-bottom">
        <div class="w-75 px-2 py-2">
          <div class="d-flex justify-content-between">
            <span><b class="text-white">Type</b></span>
            <span class="text-white">{{ clickedDetails.type }}</span>
          </div>
          <div class="d-flex justify-content-between">
            <span><b class="text-white">Area</b></span>
            <span class="text-white">{{ clickedDetails.area.toFixed(2) }} m<sup>2</sup></span>
          </div>
          <div class="d-flex justify-content-between">
            <span><b class="text-white">Perimeter</b></span>
            <span class="text-white">{{ clickedDetails.length.toFixed(2) }} m</span>
          </div>
          <div class="d-flex justify-content-between">
            <span><b class="text-white">Created On</b></span>
            <span class="text-white">{{ clickedDetails.creation_date }}</span>
          </div>
        </div>
        <div class="w-25 d-flex justify-content-center">
          <!-- <div role="button" class="text-light p-2" @click="downloadMeasure(clickedDetails)" v-b-tooltip.hover title="Download Measure">
              <i class="fa fa-download"></i>
            </div> -->
          <div
            role="button"
            class="text-light p-2"
            v-b-tooltip.hover
            title="Delete Measurement"
            @click="showDeleteModal(clickedDetails)">
            <i class="fa fa-trash"></i>
          </div>
        </div>
      </div>
    </div>
    <b-modal
      id="deleteConfirmModal"
      no-close-on-esc
      no-close-on-backdrop
      centered
      title="Deletion Confirmation"
      :hide-footer="true">
      <deleteConfirmModal :toBeDeleted="toBeDeleted" :id="toBeDeletedId" :deleteAPI="deleteAoi" />
    </b-modal>
  </div>
</template>

<script>
import deleteConfirmModal from '../../../shared/deleteConfirmModal';
export default {
  name: 'measureSideNav',
  components: { deleteConfirmModal },
  props: {
    measuresArray: Array,
  },
  data() {
    return {
      clickedDetailsVisible: false,
      clickedDetails: {},
      clickedItemKey: '',
      measureIconClasses: {
        poly: 'fa fa-draw-polygon',
        line: 'fa fa-slash',
        point: 'far fa-dot-circle',
      },
      toBeDeleted: '',
      toBeDeletedId: '',
      deleteAoi: 'airmapStore/deleteMeasureApi',
      callOnce: 1,
    };
  },
  computed: {
    currentDate() {
      const currentDate = this.$store.state.airmapStore.currentDate;
      return currentDate ? currentDate : '';
    },
  },
  methods: {
    measureItemClicked(measureItem) {
      this.clickedItemKey = this.currentDate + measureItem.label;
      this.clickedDetails = measureItem;
      this.clickedDetailsVisible = true;
      this.$root.$emit('measureItemClicked', measureItem);
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('measureClosed');
    },
    downloadMeasure(measure) {
      this.$store.dispatch('airmapStore/downloadMeasureApi', measure.id);
    },
    showDeleteModal(measureItem) {
      this.toBeDeleted = measureItem.label;
      this.toBeDeletedId = measureItem.id;
      this.$bvModal.show('deleteConfirmModal');
    },
    createNewMeasure() {
      this.$root.$emit('createNewMeasure');
    },
    removeMeaure(measureLabel) {
      if (this.clickedDetails.label === measureLabel) {
        this.clickedDetails = {};
        this.clickedDetailsVisible = false;
      }
    },
  },
  mounted() {
    this.$root.$on('measureDeleted', measureLabel => {
      this.removeMeaure(measureLabel);
    });
  },
  unmounted() {
    if (this.callOnce) {
      this.callOnce = null;
      this.$root.$emit('measureClosed');
    }
  },
};
</script>

<style scoped></style>
