<template>
  <b-form>
    <b-row>
      <b-col cols="12" md="8">
        <b-form-group label="Project Name:" label-for="name-input">
          <b-form-input
            class="w-100"
            id="name-input"
            v-model="form.name"
            placeholder="Enter project name"
            @blur="v$.form.name.$touch()"></b-form-input>
          <b-form-invalid-feedback :state="validateState('name')">
            Name must be at least 5 characters long.
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="4">
        <b-form-group label="Category:" label-for="category-input">
          <b-form-select
            id="category-input"
            v-model="form.category"
            :options="
              [{ text: 'Please select a category', value: null, disabled: true }].concat(options)
            "
            @blur="v$.form.category.$touch()"></b-form-select>
          <b-form-invalid-feedback :state="validateState('category')">
            Please select a category type
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
    </b-row>
    <b-row>
      <b-col cols="12">
        <b-form-group label="Description:" label-for="description-input">
          <b-form-textarea
            class="w-100"
            id="description-input"
            v-model="form.description"
            placeholder="Enter project description"
            @blur="v$.form.description.$touch()"></b-form-textarea>
          <b-form-invalid-feedback :state="validateState('description')">
            Description must be at least 5 characters long.
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
    </b-row>
    <b-row>
      <b-col cols="12" md="4">
        <b-form-group label="Country:" label-for="country-input">
          <b-form-select
            id="country-input"
            v-model="form.country"
            :options="
              [{ text: 'Please select a country', value: null, disabled: true }].concat(
                countriesList
              )
            "
            @change="getStatesList"
            @blur="v$.form.country.$touch()"></b-form-select>
          <b-form-invalid-feedback :state="validateState('country')">
            Please select a country
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="4">
        <b-form-group label="State:" label-for="state-input">
          <b-form-select
            id="state-input"
            v-model="form.state"
            :disabled="v$.form.country.$invalid"
            :options="
              [{ text: 'Please select a state', value: null, disabled: true }].concat(statesList)
            "
            @change="getCitiesList"
            @blur="v$.form.state.$touch()"></b-form-select>
          <b-form-invalid-feedback :state="validateState('state')">
            Please select a state
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="4">
        <b-form-group label="City:" label-for="city-input">
          <b-form-select
            id="city-input"
            v-model="form.city"
            :disabled="v$.form.state.$invalid"
            :options="
              [{ text: 'Please select a city', value: null, disabled: true }].concat(citiesList)
            "
            @blur="v$.form.city.$touch()"></b-form-select>
          <b-form-invalid-feedback :state="validateState('city')">
            Please select a city
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
    </b-row>
    <div class="text-center">
      <b-button class="primaryBg" @click="onSubmit()" :disabled="v$.form.$invalid">
        Submit
      </b-button>
    </div>
  </b-form>
</template>

<script>
import { CATEGORY } from '@/services/constant';
import { useVuelidate } from '@vuelidate/core';
import { minLength, required } from '@vuelidate/validators';
import { City, Country, State } from 'country-state-city';

export default {
  name: 'createProject',
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      form: {
        name: null,
        category: null,
        description: null,
        lastname: null,
        country: null,
        state: null,
        city: null,
      },
      options: [
        ...Object.values(CATEGORY).map(format => ({ value: format, text: format.toUpperCase() })),
      ],
      countriesList: [],
      statesList: [],
      citiesList: [],
    };
  },
  validations() {
    return {
      form: {
        name: {
          required,
          minLength: minLength(5),
        },
        category: {
          required,
        },
        description: {
          required,
          minLength: minLength(5),
        },
        country: {
          required,
        },
        state: {
          required,
        },
        city: {
          required,
        },
      },
    };
  },
  methods: {
    validateState(name) {
      const field = this.v$.form[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    fetchCountries() {
      const countriesList = Country.getAllCountries();
      this.countriesList = countriesList.map(country => ({
        text: country.name,
        value: country.isoCode,
      }));
    },
    getStatesList() {
      this.form.state = null;
      this.form.city = null;
      const statesList = State.getStatesOfCountry(this.form.country);
      this.statesList = statesList.map(state => ({
        text: state.name,
        value: state.isoCode,
      }));
    },
    getCitiesList() {
      this.form.city = null;
      const citiesList = City.getCitiesOfState(this.form.country, this.form.state);
      this.citiesList = citiesList.map(city => ({
        text: city.name,
        value: city.name,
      }));
    },
    onSubmit() {
      const country = Country.getCountryByCode(this.form.country).name;
      const state = State.getStatesOfCountry(this.form.country).find(
        s => s.isoCode === this.form.state
      ).name;
      const projectData = {
        name: this.form.name,
        description: this.form.description,
        category: this.form.category,
        country: country,
        state: state,
        city: this.form.city,
      };
      this.$store
        .dispatch('homeStore/createProjectApi', projectData)
        .then(response => {
          this.$root.$emit('showToast', {
            name: response.message,
            message: `Your project "${projectData.name} is created successfully`,
            variant: 'success',
          });
          this.$bvModal.hide('createProjectModal');
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            title: error.message,
            message:
              'There has been an error in creating your project. Try again or contact support',
            variant: 'danger',
          });
        });
    },
  },
  mounted() {
    this.fetchCountries();
  },
};
</script>

<style scoped></style>
