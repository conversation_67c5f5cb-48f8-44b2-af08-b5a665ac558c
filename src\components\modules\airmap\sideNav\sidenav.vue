<template>
  <div class="sidenav">
    <div class="my-2 mx-1">
      <b-img :src="imageURL" rounded thumbnail></b-img>
    </div>
    <div class="d-flex flex-column justify-content-between align-items-center">
      <div>
        <ng-container v-if="isAdmin || isManager">
          <div
            v-for="link in navbarLinks"
            :key="link.header"
            @click.prevent="switchSideNav(link.header)"
            class="sidenavLinks"
            :class="{ clickedSideNav: sideBarHeader === link.header }"
            :title="link.header"
            v-b-tooltip.hover.right>
            <i :class="link.icon"></i>
          </div>
        </ng-container>
      </div>
      <div style="position: fixed; bottom: 0">
        <div class="sidenavLinks" @click="showInfo()" title="Project Info" v-b-tooltip.hover.right>
          <i class="fas fa-info-circle icon-color"></i>
        </div>
        <div class="sidenavLinks" @click="$router.go(-1)" title="Go Back" v-b-tooltip.hover.right>
          <i class="fas fa-arrow-left icon-color"></i>
        </div>
      </div>
    </div>
    <subSideNav :sideBarVisible="sideBarVisible" :sideBarHeader="sideBarHeader" />
    <b-modal
      id="csvUploadModel"
      :title="`Report Download for ${currentDateProject.type} (${currentDateProject.date})`"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <csvUploadModel :fileObj="currentDateProject.properties?.files" />
    </b-modal>
    <div class="projectInfoContainer" v-if="isProjectInfoShown">
      <p class="m-1">
        Project Name :
        <span class="font-weight-bold text-dark">{{ nameFormat(projectData.name) }}</span>
      </p>
      <p class="m-1">
        Description : <span class="font-weight-bold text-dark">{{ projectData.description }}</span>
      </p>
      <p class="m-1">
        Location :
        <span class="font-weight-bold text-dark">
          {{ projectData.city }}, {{ projectData.state }},
          {{ projectData.country }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import { CATEGORY, SOLAR_TYPE, formatProjectName } from '@/services/constant';
import { mapGetters } from 'vuex';

const subSideNav = () => import('./subSideNav.vue');
const csvUploadModel = () => import('../csvUploadmodel.vue');

export default {
  name: 'sidenav',
  components: {
    subSideNav,
    csvUploadModel,
  },
  data() {
    return {
      sidebarId: '',
      sideBarHeader: '',
      sideBarVisible: false,
      clickedSideNav: '',
      sideBarContent: {},
      isProjectInfoShown: false,
    };
  },
  computed: {
    ...mapGetters('userStore', ['isAdmin', 'isManager']),
    projectData() {
      return this.$store.state.homeStore.projectData;
    },
    imageURL() {
      return this.$store.state.userStore.loggedInUser?.sidebar_logo;
    },
    currentDateProject() {
      return this.$store.state.homeStore.projectData.currentDateProject;
    },
    navbarLinks() {
      const commonLinks = [
        { header: 'Compare', icon: 'fa fa-arrows-h icon-color' },
        { header: 'Analytics', icon: 'fa fa-chart-bar icon-color' },
        { header: 'Area of Intrest', icon: 'fa fa-object-group icon-color' },
        // { header: 'Activities', icon: 'fas fa-tasks icon-color' },
        { header: 'Report Download', icon: 'fas fa-download icon-color' },
        { header: 'Current Field Status / Time Lapse View', icon: 'fas fa-video icon-color' },
        { header: 'Outer / Inner Obliques Images', icon: 'fas fa-images icon-color' },
      ];
      let specificLinks = [];
      if (this.currentDateProject.type === SOLAR_TYPE.SCQM) {
        specificLinks = [
          { header: 'Deviation', icon: 'fa fa-map-signs icon-color' },
          { header: 'Sub Camp Deviation', icon: 'fa fa-random icon-color' },
        ];
      } else {
        specificLinks = [
          { header: 'Summary', icon: 'fas fa-chart-pie icon-color' },
          { header: 'Sub Camp', icon: 'fas fa-adjust icon-color' },
        ];
      }
      const linksToReturn =
        this.projectData.category === CATEGORY.SOLAR
          ? [...specificLinks, ...commonLinks]
          : [{ header: 'Assets', icon: 'fas fa-chart-pie icon-color' }, ...commonLinks];
      return linksToReturn;
    },
    nameFormat() {
      return originalName => {
        return formatProjectName(originalName);
      };
    },
  },
  methods: {
    showInfo() {
      this.closeSideBar();
      this.isProjectInfoShown = !this.isProjectInfoShown;
    },
    switchSideNav(header) {
      this.isProjectInfoShown = false;
      const trimmedHeader = header.trim();
      const currentHeader = this.sideBarHeader.trim();
      if (currentHeader === trimmedHeader) {
        this.closeSideBar();
      } else {
        const visibleHeaders = [
          'Summary',
          'Assets',
          'Deviation',
          'Sub Camp',
          'Sub Camp Deviation',
          'Activities',
          'Area of Intrest',
          'CAD',
        ];
        if (visibleHeaders.includes(trimmedHeader)) {
          this.sideBarVisible = true;
        } else if (trimmedHeader === 'Compare') {
          this.sideBarVisible = false;
          this.$store.dispatch('homeStore/setCompareData', {
            date: this.currentDateProject.date,
            side: 'left',
          });
          this.$store.dispatch('homeStore/setCompareData', {
            date: this.currentDateProject.date,
            side: 'right',
          });
          this.$router.push('/compare');
        } else if (trimmedHeader === 'Analytics') {
          this.sideBarVisible = false;
          this.$router.push('/analytics');
        } else if (trimmedHeader === 'Report Download') {
          this.sideBarVisible = false;
          this.$bvModal.show('csvUploadModel');
        } else if (trimmedHeader === 'Current Field Status / Time Lapse View') {
          this.sideBarVisible = false;
          this.$root.$emit('openVideo');
        } else if (trimmedHeader === 'Outer / Inner Obliques Images') {
          this.sideBarVisible = false;
          this.$root.$emit('openImages');
        }
        this.sideBarHeader = trimmedHeader;
      }
    },
    closeSideBar() {
      this.sideBarVisible = false;
      this.sideBarHeader = '';
    },
  },
  mounted() {
    this.$root.$on('closeSideBar', () => {
      this.closeSideBar();
    });
  },
};
</script>

<style scoped>
.sidenav {
  width: 70px;
  height: 100%;
  position: fixed;
  left: 0;
  z-index: 999;
  top: 0px;
}
.sidenavLinks {
  padding: 15px !important;
  cursor: pointer;
  text-align: center;
}
.sidenavLinks:hover {
  color: var(--primary);
}
.icon-color {
  color: rgb(3, 34, 92);
}
.projectInfoContainer {
  position: fixed;
  left: 70px;
  bottom: 5px;
  background-color: var(--white);
  padding: 5px;
  color: var(--primary);
}
</style>
