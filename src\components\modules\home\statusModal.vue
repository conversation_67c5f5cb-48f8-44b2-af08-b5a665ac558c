<template>
  <div>
    <div class="d-flex justify-content-center">
      <b-form inline>
        <label class="mr-4" for="category-input">Select Type & Date:</label>
        <b-form-select
          id="category-input"
          v-model="selectedDate"
          @change="dateSelected"
          :options="datesArray"></b-form-select>
      </b-form>
    </div>
    <div class="d-flex justify-content-center" v-if="selectedDate && selectedStatus">
      <div class="m-3 w-75">
        <section class="step-indicator">
          <div class="step step1" :class="{ active: selectedStatus.statusNumber >= 1 }">
            <div class="step-icon"><i class="fas fa-cloud-upload-alt"></i></div>
            <p class="m-0">Uploaded</p>
          </div>
          <div class="indicator" :class="{ active: selectedStatus.statusNumber >= 1 }"></div>
          <div class="step step2" :class="{ active: selectedStatus.statusNumber >= 2 }">
            <div class="step-icon"><i class="fas fa-drafting-compass"></i></div>
            <p class="m-0">Orthomosaic</p>
          </div>
          <div class="indicator" :class="{ active: selectedStatus.statusNumber >= 2 }"></div>
          <div class="step step3" :class="{ active: selectedStatus.statusNumber >= 3 }">
            <div class="step-icon"><i class="fas fa-check-circle"></i></div>
            <p class="m-0">Completed</p>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'statusModal',
  props: {
    statusArray: { type: Array },
  },
  data() {
    return {
      statusMap: new Map([
        ['Incomplete', 0],
        ['FTP', 1],
        ['Orthomosaic', 2],
        ['Complete', 3],
      ]),
      selectedDate: null,
      selectedStatus: null,
    };
  },
  computed: {
    datesArray() {
      const datesArray = this.statusArray.map(statusData => ({
        value: `${statusData.date}/${statusData.type}`,
        text: `${statusData.date} --- ${statusData.type}`,
      }));
      datesArray.unshift({ value: null, text: 'Please select a date', disabled: true });
      return datesArray;
    },
  },
  methods: {
    dateSelected(date) {
      if (date) {
        const [selectedDate, selectedType] = date.split('/');
        this.selectedStatus = this.statusArray.find(
          s => s.date === selectedDate && s.type === selectedType
        );
        this.selectedStatus = {
          ...this.selectedStatus,
          statusNumber: this.statusMap.get(this.selectedStatus.status),
        };
      }
    },
  },
};
</script>

<style scoped>
.step-indicator {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  gap: 0;
  flex-wrap: nowrap;
}
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.step-icon {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background: #c2c2c2;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
}
.step.active .step-icon {
  background: var(--primary);
}
.step p {
  margin-top: 8px;
  color: var(--grey);
  font-size: 14px;
  font-weight: 700;
}
.step.active p {
  color: var(--dark);
}
.indicator {
  flex-grow: 1;
  height: 2px;
  background-color: #c2c2c2;
  margin: 0 -1px;
}
.indicator.active {
  background-color: var(--primary);
}
@media (max-width: 767px) {
  .step-indicator {
    flex-direction: column;
    align-items: center;
  }
  .indicator {
    width: 2px;
    height: 30px;
    margin: -1px 0;
  }
  .step p {
    font-size: 12px;
  }
  .custom-select,
  .form-select {
    min-height: 44px;
    font-size: 16px;
  }
  .step-icon {
    height: 40px;
    width: 40px;
    font-size: 16px;
  }
}
</style>
