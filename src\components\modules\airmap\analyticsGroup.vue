<template>
  <div>
    <b-row class="gy-3">
      <b-col cols="12" md="6" lg="3">
        <b-form-group class="font-weight-bold" label="Category:">
          <b-form-select v-model="selectedCategory" @change="updateFeatures">
            <b-form-select-option value="SCPM">SCPM</b-form-select-option>
            <b-form-select-option value="SCQM">SCQM</b-form-select-option>
          </b-form-select>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="6" lg="3">
        <b-form-group class="font-weight-bold" label="View:">
          <b-form-select v-model="selectedView" @change="updateFeatures">
            <b-form-select-option value="global">Global</b-form-select-option>
            <b-form-select-option value="inverter">Inverter-wise</b-form-select-option>
          </b-form-select>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="6" lg="3">
        <b-form-group class="font-weight-bold" label="Date:">
          <b-form-select v-model="selectedDate" @change="updateChart">
            <b-form-select-option v-for="date in availableDates" :key="date" :value="date">
              {{ date }}
            </b-form-select-option>
          </b-form-select>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="6" lg="3" v-if="invertersList.length && selectedView === 'inverter'">
        <b-form-group class="font-weight-bold" label="Inverter:">
          <b-form-select v-model="selectedInverter" @change="updateFeatures">
            <b-form-select-option v-for="inv in invertersList" :key="inv" :value="inv">
              {{ inv }}
            </b-form-select-option>
          </b-form-select>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="6" lg="3" v-if="availableFeatures.length">
        <b-form-group class="font-weight-bold" label="Feature:">
          <b-form-select v-model="selectedFeature" @change="updateChart">
            <b-form-select-option
              v-for="feature in availableFeatures"
              :key="feature"
              :value="feature">
              {{ feature }}
            </b-form-select-option>
          </b-form-select>
        </b-form-group>
      </b-col>
    </b-row>
    <div class="border rounded-lg shadow p-3 d-flex justify-content-center flex-wrap">
      <div v-if="chart" ref="plotlyChart" class="chartLayout"></div>
      <div v-else>
        <h4 class="text-center font-weight-bold m-3">
          No data available for the selected criteria
        </h4>
      </div>
    </div>
  </div>
</template>

<script>
import { CHART_COLORS, SOLAR_TYPE, VIEWS } from '@/services/constant';
import Plotly from 'plotly.js-dist-min';

export default {
  name: 'analyticsGroup',
  data() {
    return {
      selectedCategory: SOLAR_TYPE.SCPM,
      selectedView: VIEWS.GLOBAL,
      selectedInverter: '',
      selectedFeature: '',
      selectedDate: '',
      chart: null,
      availableFeatures: [],
      plotlyInstance: null,
      scqm_feature_total: 0,
    };
  },
  computed: {
    projectData() {
      return this.$store.state.homeStore.projectData.projectdata;
    },
    isSCQM() {
      return this.selectedCategory === SOLAR_TYPE.SCQM;
    },
    filteredData() {
      return this.projectData
        .filter(item => item.type === this.selectedCategory)
        .sort((a, b) => new Date(a.date) - new Date(b.date));
    },
    availableDates() {
      return this.filteredData.map(item => item.date);
    },
    invertersList() {
      const latestData = this.getLatestData();
      if (!latestData) return [];
      return this.selectedCategory === SOLAR_TYPE.SCPM
        ? Object.keys(latestData.inverter?.Inverter || {})
        : Object.keys(latestData.inverter?.data || {});
    },
    currentData() {
      return this.filteredData.find(item => item.date === this.selectedDate);
    },
  },
  methods: {
    getLatestData() {
      const filteredData = this.filteredData;
      return filteredData.length ? filteredData[filteredData.length - 1] : null;
    },
    extractDataPoint(dataPoint) {
      if (this.selectedView === VIEWS.GLOBAL) {
        return this.selectedCategory === SOLAR_TYPE.SCPM
          ? dataPoint?.summary?.summary
          : dataPoint?.summary?.data?.Summary;
      }
      return this.selectedCategory === SOLAR_TYPE.SCPM
        ? dataPoint?.inverter?.Inverter?.[this.selectedInverter]
        : dataPoint?.inverter?.data?.[this.selectedInverter];
    },
    updateFeatures() {
      const data = this.extractDataPoint(this.currentData);
      if (!data) {
        this.availableFeatures = [];
        this.selectedFeature = '';
        this.chart = null;
        if (this.plotlyInstance) {
          Plotly.purge(this.$refs.plotlyChart);
          this.plotlyInstance = null;
        }
        return;
      }
      this.availableFeatures = Object.keys(data);
      if (!this.availableFeatures.includes(this.selectedFeature)) {
        this.selectedFeature = this.availableFeatures[0] || '';
      }
      this.updateChart();
    },
    getSubFeatures(featureData) {
      if (!featureData) return [];
      if (featureData.properties) {
        return Object.keys(featureData.properties);
      }
      return this.selectedCategory === SOLAR_TYPE.SCPM ? Object.keys(featureData) : [];
    },
    updateChart() {
      if (!this.canUpdateChart()) {
        this.chart = null;
        if (this.plotlyInstance) {
          Plotly.purge(this.$refs.plotlyChart);
          this.plotlyInstance = null;
        }
        return;
      }
      const data = this.extractDataPoint(this.currentData);
      const featureData = data[this.selectedFeature];
      if (this.isSCQM) {
        this.scqm_feature_total = featureData['total'];
      }
      const subFeatures = this.getSubFeatures(featureData);

      const chartData = this.createChartData(subFeatures, featureData);
      const layout = this.createChartLayout();
      const config = {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'zoom2d',
          'pan2d',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'resetScale2d',
        ],
        toImageButtonOptions: {
          filename: `${this.selectedFeature} Analysis: ${this.selectedDate}`,
          scale: 5,
        },
      };
      this.chart = true;
      this.$nextTick(() => {
        if (this.plotlyInstance) {
          Plotly.react(this.$refs.plotlyChart, chartData, layout, config);
        } else {
          this.plotlyInstance = Plotly.newPlot(this.$refs.plotlyChart, chartData, layout, config);
        }
      });
    },
    createChartData(subFeatures, featureData) {
      const isSCPM = this.selectedCategory === SOLAR_TYPE.SCPM;
      const labels = {
        label1: isSCPM ? 'Installed' : 'Count',
        label2: isSCPM ? 'Remaining' : 'Non-Deviated',
      };
      if (this.selectedCategory === SOLAR_TYPE.SCPM) {
        const actualValues = subFeatures.map(sf => featureData[sf].Actual);
        const targetRemainingValues = subFeatures.map(
          sf => featureData[sf].Total - featureData[sf].Actual
        );
        const totalValues = subFeatures.map(sf => featureData[sf].Total);
        return [
          {
            x: subFeatures,
            y: actualValues,
            name: labels.label1,
            type: 'bar',
            marker: { color: CHART_COLORS.ACTUAL },
            text: actualValues.map((val, idx) => `${((val / totalValues[idx]) * 100).toFixed(1)}%`),
            textposition: 'inside',
            insidetextanchor: 'middle',
            hovertemplate: '%{y} (%{text}) / %{customdata}',
            customdata: totalValues,
          },
          {
            x: subFeatures,
            y: targetRemainingValues,
            name: labels.label2,
            type: 'bar',
            marker: { color: CHART_COLORS.TARGET },
            text: targetRemainingValues.map(
              (val, idx) => `${((val / totalValues[idx]) * 100).toFixed(1)}%`
            ),
            textposition: 'inside',
            insidetextanchor: 'middle',
            hovertemplate: '%{y} (%{text}) / %{customdata}',
            customdata: totalValues,
          },
        ];
      } else {
        const count = subFeatures.map(sf => featureData.properties[sf].count);
        return [
          {
            x: subFeatures,
            y: count,
            name: labels.label1,
            type: 'bar',
            marker: { color: CHART_COLORS.ACTUAL },
            text: count.map(sf => `${sf} (${((sf / this.scqm_feature_total) * 100).toFixed(1)}%)`),
            textposition: 'inside',
            insidetextanchor: 'middle',
            hovertemplate: '%{text}',
          },
        ];
      }
    },
    createChartLayout() {
      const totalCountText = this.isSCQM
        ? `(Total Deviated Count - ${this.scqm_feature_total})`
        : '';
      const title = `${this.selectedFeature} Analysis: ${this.selectedDate} ${totalCountText}`;
      return {
        title: title,
        xaxis: {
          title: 'Sub-Features',
          type: 'category',
          automargin: true,
        },
        yaxis: {
          title: this.getYAxisTitle(this.selectedFeature),
        },
        legend: {
          x: 0,
          y: 1.2,
        },
        automargin: true,
        showlegend: true,
        hovermode: 'closest',
        barmode: 'stack',
        bargap: 0.15,
        bargroupgap: 0.1,
        autosize: false,
        width: 1000,
        height: 400,
        margin: {
          t: 50,
          b: 75,
          l: 75,
          r: 25,
        },
      };
    },
    canUpdateChart() {
      return this.selectedFeature && this.selectedDate && this.currentData;
    },
    getYAxisTitle(category) {
      if (category?.toLowerCase() === 'cables') {
        return 'Length (meters)';
      }
      return 'Count';
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.availableDates.length) {
        this.selectedDate = this.availableDates[0];
      }
      if (this.invertersList.length) {
        this.selectedInverter = this.invertersList[0];
      }
      this.updateFeatures();
    });
  },
  beforeUnmount() {
    if (this.plotlyInstance) {
      Plotly.purge(this.$refs.plotlyChart);
    }
  },
  watch: {
    selectedCategory() {
      this.updateFeatures();
      if (this.availableDates.length) {
        this.selectedDate = this.availableDates[0];
      }
    },
    selectedView() {
      this.updateFeatures();
    },
    selectedInverter() {
      if (this.selectedView === VIEWS.INVERTER) {
        this.updateFeatures();
      }
    },
    invertersList: {
      immediate: true,
      handler(newList) {
        if (newList.length && !this.selectedInverter) {
          this.selectedInverter = newList[0];
        }
      },
    },
  },
};
</script>

<style scoped></style>
