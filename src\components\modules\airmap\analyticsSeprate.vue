<template>
  <div>
    <b-row class="gy-3">
      <b-col cols="12">
        <b-row class="gy-3">
          <b-col cols="12" md="3">
            <b-form-group class="font-weight-bold" label="Graph Type:">
              <b-form-select v-model="selectedGraph" @change="updateChart">
                <b-form-select-option value="line">Line Chart</b-form-select-option>
                <b-form-select-option value="stacked">Stacked Bar</b-form-select-option>
              </b-form-select>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="3">
            <b-form-group class="font-weight-bold" label="Category:">
              <b-form-select v-model="selectedCategory" @change="updateFeatures">
                <b-form-select-option value="SCPM">SCPM</b-form-select-option>
                <b-form-select-option value="SCQM">SCQM</b-form-select-option>
              </b-form-select>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="3">
            <b-form-group class="font-weight-bold" label="View:">
              <b-form-select v-model="selectedView" @change="updateFeatures">
                <b-form-select-option value="global">Global</b-form-select-option>
                <b-form-select-option value="inverter">Inverter-wise</b-form-select-option>
              </b-form-select>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="3" v-if="invertersList.length && selectedView === 'inverter'">
            <b-form-group class="font-weight-bold" label="Inverter:">
              <b-form-select v-model="selectedInverter" @change="updateFeatures">
                <b-form-select-option v-for="inv in invertersList" :key="inv" :value="inv">
                  {{ inv }}
                </b-form-select-option>
              </b-form-select>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="3" v-if="availableFeatures.length">
            <b-form-group class="font-weight-bold" label="Feature:">
              <b-form-select v-model="selectedFeature" @change="updateSubFeatures">
                <b-form-select-option
                  v-for="feature in availableFeatures"
                  :key="feature"
                  :value="feature">
                  {{ feature }}
                </b-form-select-option>
              </b-form-select>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="3" v-if="availableSubFeatures.length">
            <b-form-group class="font-weight-bold" label="Sub-Feature:">
              <b-form-select v-model="selectedSubFeature" @change="updateChart">
                <b-form-select-option
                  v-for="subFeature in availableSubFeatures"
                  :key="subFeature"
                  :value="subFeature">
                  {{ subFeature }}
                </b-form-select-option>
              </b-form-select>
            </b-form-group>
          </b-col>
        </b-row>
      </b-col>
      <b-col cols="12">
        <b-form-group class="font-weight-bold" label="Select Dates:">
          <div class="date-selector">
            <div class="select-buttons date-selection-header text-center">
              <span class="date-count-info">
                {{ selectedDates.length }} of {{ relevantDatesForSubFeature.length }} dates selected
              </span>
              <div>
                <b-button
                  variant="outline-primary"
                  size="sm"
                  class="mr-2 mb-2"
                  @click="selectAllDates"
                  :disabled="selectedDates.length === relevantDatesForSubFeature.length">
                  Select All
                </b-button>
                <b-button
                  variant="outline-danger"
                  size="sm"
                  class="mb-2"
                  @click="deselectAllDates"
                  :disabled="selectedDates.length === 0">
                  Deselect All
                </b-button>
              </div>
            </div>
            <b-form-checkbox-group v-model="selectedDates" @change="updateChart">
              <b-form-checkbox v-for="date in relevantDatesForSubFeature" :key="date" :value="date">
                {{ date }}
              </b-form-checkbox>
            </b-form-checkbox-group>
          </div>
          <template #description>
            <small class="text-muted">
              Only dates with available data for the selected sub-feature are shown
            </small>
          </template>
        </b-form-group>
      </b-col>
    </b-row>
    <div class="border rounded-lg shadow p-3 d-flex justify-content-center flex-wrap">
      <div v-if="chart" ref="plotlyChart" class="chartLayout"></div>
      <div v-else>
        <h4 class="text-center font-weight-bold m-3">
          No data available for the selected criteria
        </h4>
      </div>
    </div>
  </div>
</template>

<script>
import { CHART_COLORS, CHART_TYPES, SOLAR_TYPE, VIEWS } from '@/services/constant';
import Plotly from 'plotly.js-dist-min';

export default {
  name: 'analyticsSeprate',
  data() {
    return {
      selectedGraph: CHART_TYPES.LINE,
      selectedCategory: SOLAR_TYPE.SCPM,
      selectedView: VIEWS.GLOBAL,
      selectedInverter: '',
      selectedFeature: '',
      selectedSubFeature: '',
      selectedDates: [],
      chart: null,
      availableFeatures: [],
      availableSubFeatures: [],
      plotlyInstance: null,
      isDeselectingAll: false,
    };
  },
  computed: {
    projectData() {
      return this.$store.state.homeStore.projectData.projectdata;
    },
    filteredData() {
      return this.projectData
        .filter(item => item.type === this.selectedCategory)
        .sort((a, b) => new Date(a.date) - new Date(b.date));
    },
    availableDates() {
      return this.filteredData.map(item => item.date);
    },
    relevantDatesForSubFeature() {
      if (!this.selectedFeature || !this.selectedSubFeature) {
        return this.availableDates;
      }
      const datesWithSubFeature = [];
      this.filteredData.forEach(dataPoint => {
        const extractedData = this.getExtractedData(dataPoint);
        if (extractedData && extractedData[this.selectedFeature]) {
          const featureData = extractedData[this.selectedFeature];
          const hasSubFeature = this.checkSubFeatureExists(featureData, this.selectedSubFeature);

          if (hasSubFeature) {
            datesWithSubFeature.push(dataPoint.date);
          }
        }
      });
      return datesWithSubFeature;
    },
    relevantDataPoints() {
      const datesToConsider =
        this.selectedDates.length > 0 ? this.selectedDates : this.availableDates;

      return this.filteredData.filter(item => datesToConsider.includes(item.date));
    },
    invertersList() {
      const allInverters = new Set();
      this.relevantDataPoints.forEach(dataPoint => {
        const inverters =
          this.selectedCategory === SOLAR_TYPE.SCPM
            ? Object.keys(dataPoint.inverter?.Inverter || {})
            : Object.keys(dataPoint.inverter?.data || {});

        inverters.forEach(inv => allInverters.add(inv));
      });
      return Array.from(allInverters).sort();
    },
    currentData() {
      const filtered = this.filteredData
        .map(dataPoint => {
          const date = new Date(dataPoint.date);
          if (isNaN(date.getTime())) {
            if (process.env.NODE_ENV === 'development') {
              console.error(`Invalid date found: ${dataPoint.date}`);
            }
            return null;
          }
          return {
            date: dataPoint.date,
            data: this.extractDataPoint(dataPoint),
          };
        })
        .filter(Boolean);
      return this.selectedDates.length > 0
        ? filtered.filter(item => this.selectedDates.includes(item.date))
        : [];
    },
  },
  methods: {
    getExtractedData(dataPoint) {
      if (this.selectedView === VIEWS.GLOBAL) {
        return this.selectedCategory === SOLAR_TYPE.SCPM
          ? dataPoint?.summary?.summary
          : dataPoint?.summary?.data?.Summary;
      }
      if (!this.selectedInverter) return null;
      return this.selectedCategory === SOLAR_TYPE.SCPM
        ? dataPoint?.inverter?.Inverter?.[this.selectedInverter]
        : dataPoint?.inverter?.data?.[this.selectedInverter];
    },
    getAllFeatures() {
      const allFeatures = new Set();
      this.relevantDataPoints.forEach(dataPoint => {
        const extractedData = this.getExtractedData(dataPoint);
        if (extractedData) {
          Object.keys(extractedData).forEach(f => allFeatures.add(f));
        }
      });
      return Array.from(allFeatures).sort();
    },
    getAllSubFeatures(featureName) {
      const allSubFeatures = new Set();
      this.relevantDataPoints.forEach(dataPoint => {
        const extractedData = this.getExtractedData(dataPoint);
        if (extractedData && extractedData[featureName]) {
          const subFeatures = this.getSubFeatures(extractedData[featureName]);
          subFeatures.forEach(sf => allSubFeatures.add(sf));
        }
      });
      return Array.from(allSubFeatures).sort();
    },
    checkSubFeatureExists(featureData, subFeatureName) {
      if (!featureData || !subFeatureName) return false;
      const hasOwn = Object.prototype.hasOwnProperty;
      if (featureData.properties) {
        return hasOwn.call(featureData.properties, subFeatureName);
      }
      return hasOwn.call(featureData, subFeatureName);
    },
    selectAllDates() {
      this.selectedDates = [...this.relevantDatesForSubFeature];
      this.updateChart();
    },
    deselectAllDates() {
      this.isDeselectingAll = true; // Set flag before clearing
      this.selectedDates = [];
      this.$nextTick(() => {
        this.isDeselectingAll = false; // Reset flag after Vue updates
      });
      this.updateChart();
    },
    extractDataPoint(dataPoint) {
      if (this.selectedView === VIEWS.GLOBAL) {
        return this.selectedCategory === SOLAR_TYPE.SCPM
          ? dataPoint?.summary?.summary
          : dataPoint?.summary?.data?.Summary;
      }
      return this.selectedCategory === SOLAR_TYPE.SCPM
        ? dataPoint?.inverter?.Inverter?.[this.selectedInverter]
        : dataPoint?.inverter?.data?.[this.selectedInverter];
    },
    updateFeatures() {
      this.availableFeatures = this.getAllFeatures();
      if (!this.availableFeatures.includes(this.selectedFeature)) {
        this.selectedFeature = this.availableFeatures[0] || '';
      }
      if (!this.selectedFeature) {
        this.resetSubFeatures();
        this.chart = null;
        return;
      }
      this.updateSubFeatures();
    },
    updateSubFeatures() {
      if (!this.selectedFeature) {
        this.resetSubFeatures();
        return;
      }
      this.availableSubFeatures = this.getAllSubFeatures(this.selectedFeature);
      if (!this.availableSubFeatures.includes(this.selectedSubFeature)) {
        this.selectedSubFeature = this.availableSubFeatures[0] || '';
      }
      this.updateChart();
    },
    resetSubFeatures() {
      this.availableSubFeatures = [];
      this.selectedSubFeature = '';
      if (this.plotlyInstance) {
        Plotly.purge(this.$refs.plotlyChart);
        this.plotlyInstance = null;
      }
    },
    getSubFeatures(data) {
      return data?.properties ? Object.keys(data.properties) : Object.keys(data || {});
    },
    createChartConfig(dates, actualValues, totalValues, chartType) {
      if (!dates.length || !actualValues.length || !totalValues.length) {
        return null;
      }
      const maxValue = Math.max(
        ...totalValues.filter(Number.isFinite),
        ...actualValues.filter(Number.isFinite)
      );
      if (!Number.isFinite(maxValue)) {
        this.chart = false;
        if (process.env.NODE_ENV === 'development') {
          console.error('Invalid values detected in chart data');
        }
        return null;
      }
      const yAxisPadding = maxValue * 0.1;

      const layout = this.createBaseLayout(maxValue, yAxisPadding);
      const chartData = this.createChartData(dates, actualValues, totalValues, chartType);
      const config = {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'zoom2d',
          'pan2d',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'resetScale2d',
        ],
        toImageButtonOptions: {
          filename: `${this.getChartTitle()}: ${this.selectedFeature} - ${this.selectedSubFeature}`,
          scale: 5,
        },
      };
      this.chart = true;
      this.$nextTick(() => {
        if (this.plotlyInstance) {
          Plotly.react(this.$refs.plotlyChart, chartData, layout, config);
        } else {
          this.plotlyInstance = Plotly.newPlot(this.$refs.plotlyChart, chartData, layout, config);
        }
      });
    },
    createBaseLayout(maxValue, yAxisPadding) {
      return {
        title: {
          text: `${this.selectedFeature} - ${this.selectedSubFeature}`,
          font: { size: 16 },
        },
        xaxis: {
          title: 'Date Range',
          tickformat: '%Y-%m-%d',
          type: 'category',
          tickmode: 'auto',
          automargin: true,
        },
        yaxis: {
          title: this.getYAxisTitle(this.selectedFeature),
          range: [0, maxValue + yAxisPadding],
        },
        legend: {
          x: 0,
          y: 1.2,
        },
        automargin: true,
        showlegend: true,
        hovermode: 'closest',
        barmode: this.selectedGraph === CHART_TYPES.STACKED ? 'stack' : undefined,
        ...(this.selectedGraph !== CHART_TYPES.LINE && {
          bargap: 0.15,
          bargroupgap: 0.1,
        }),
        autosize: false,
        width: 1000,
        height: 400,
        margin: {
          t: 50,
          b: 75,
          l: 75,
          r: 25,
        },
      };
    },
    createChartData(dates, actualValues, totalValues, chartType) {
      const isSCPM = this.selectedCategory === SOLAR_TYPE.SCPM;
      const labels =
        chartType === CHART_TYPES.STACKED
          ? {
              label1: isSCPM ? 'Installed' : 'Type Deviated',
              label2: isSCPM ? 'Remaining' : 'Other Deviated',
            }
          : {
              label1: isSCPM ? 'Installed' : 'Type Deviated',
              label2: isSCPM ? 'Target' : 'Total Deviated',
            };
      if (chartType === CHART_TYPES.STACKED) {
        const remainingValues = totalValues.map((total, i) => total - actualValues[i]);
        return [
          {
            name: labels.label1,
            x: dates,
            y: actualValues,
            type: 'bar',
            marker: { color: CHART_COLORS.ACTUAL },
            text: actualValues.map((val, idx) => `${((val / totalValues[idx]) * 100).toFixed(1)}%`),
            textposition: 'inside',
            insidetextanchor: 'middle',
            hovertemplate: '%{y} (%{text}) / %{customdata}',
            customdata: totalValues,
          },
          {
            name: labels.label2,
            x: dates,
            y: remainingValues,
            type: 'bar',
            marker: { color: CHART_COLORS.TARGET },
            text: remainingValues.map(
              (val, idx) => `${((val / totalValues[idx]) * 100).toFixed(1)}%`
            ),
            textposition: 'inside',
            insidetextanchor: 'middle',
            hovertemplate: '%{y} (%{text}) / %{customdata}',
            customdata: totalValues,
          },
        ];
      }
      return [
        {
          name: labels.label1,
          x: dates,
          y: actualValues,
          text: actualValues.map(val => `${val}`),
          type: 'scatter',
          mode: 'lines+markers+text',
          line: { color: CHART_COLORS.ACTUAL, width: 2 },
          marker: { color: CHART_COLORS.ACTUAL, size: 6 },
          textposition: 'bottom center',
          hovertemplate: '%{y} / %{customdata}',
          customdata: totalValues,
        },
        {
          name: labels.label2,
          x: dates,
          y: totalValues,
          text: totalValues.map(val => `${val}`),
          type: 'scatter',
          mode: 'lines+markers+text',
          line: { color: CHART_COLORS.TARGET, width: 2 },
          marker: { color: CHART_COLORS.TARGET, size: 6 },
          textposition: 'top center',
          hovertemplate: '%{y} / %{customdata}',
          customdata: totalValues,
        },
      ];
    },
    getChartTitle() {
      return this.selectedGraph == CHART_TYPES.LINE ? 'Progress Over Time' : 'Progress Summary';
    },
    updateChart() {
      if (!this.canUpdateChart()) {
        this.chart = null;
        if (this.plotlyInstance) {
          Plotly.purge(this.$refs.plotlyChart);
          this.plotlyInstance = null;
        }
        return;
      }
      const { dates, actualValues, totalValues } = this.extractChartData();
      this.createChartConfig(dates, actualValues, totalValues, this.selectedGraph);
    },
    canUpdateChart() {
      return this.selectedFeature && this.selectedSubFeature && this.currentData.length > 0;
    },
    extractChartData() {
      const chartData = {
        dates: [],
        actualValues: [],
        totalValues: [],
      };
      this.currentData.forEach(dataPoint => {
        if (!dataPoint.data?.[this.selectedFeature]) return;
        chartData.dates.push(dataPoint.date);
        const values = this.extractValues(dataPoint.data);
        if (values) {
          chartData.actualValues.push(values.actual);
          chartData.totalValues.push(values.total);
        }
      });
      return chartData;
    },
    extractValues(data) {
      try {
        if (this.selectedCategory === SOLAR_TYPE.SCPM) {
          const scpmData = data[this.selectedFeature]?.[this.selectedSubFeature];
          if (!scpmData) return null;
          return {
            actual: Number(scpmData.Actual) || 0,
            total: Number(scpmData.Total) || 0,
          };
        }
        const featureData = data[this.selectedFeature];
        const propertyData = featureData?.properties?.[this.selectedSubFeature];
        if (!propertyData) return null;
        return {
          actual: Number(propertyData.count) || 0,
          total: Number(featureData.total) || 0,
        };
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error extracting values:', error);
        }
        return null;
      }
    },
    getYAxisTitle(category) {
      if (category?.toLowerCase() === 'cables') {
        return 'Length (meters)';
      }
      return 'Count';
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.invertersList.length) {
        this.selectedInverter = this.invertersList[0];
      }
      this.selectedDates = [...this.availableDates];
      this.updateFeatures();
    });
  },
  beforeUnmount() {
    if (this.plotlyInstance && this.$refs.plotlyChart) {
      Plotly.purge(this.$refs.plotlyChart);
      this.plotlyInstance = null;
    }
  },
  watch: {
    selectedCategory() {
      this.updateFeatures();
      this.selectedDates = [];
    },
    selectedView() {
      this.selectedDates = [...this.relevantDatesForSubFeature];
      this.updateFeatures();
    },
    selectedInverter() {
      this.selectedDates = [...this.relevantDatesForSubFeature];
      if (this.selectedView === VIEWS.INVERTER) {
        this.updateFeatures();
      }
    },
    selectedGraph() {
      this.updateChart();
    },
    selectedSubFeature() {
      this.selectedDates = [...this.relevantDatesForSubFeature];
      this.updateChart();
    },
    selectedDates: {
      handler(newDates) {
        if (
          newDates.length === 0 &&
          this.relevantDatesForSubFeature.length > 0 &&
          !this.isDeselectingAll
        ) {
          this.selectedDates = [...this.relevantDatesForSubFeature];
        }
        this.updateChart();
      },
      deep: true,
    },
    invertersList: {
      immediate: true,
      handler(newList) {
        if (newList.length && !this.selectedInverter) {
          this.selectedInverter = newList[0];
        }
      },
    },
  },
};
</script>

<style scoped>
.date-selector {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
}
.select-buttons {
  margin-bottom: 0.75rem;
  border-bottom: 3px solid #dee2e6;
}
.date-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.date-count-info {
  font-size: 0.875rem;
  font-weight: 600;
}
</style>
