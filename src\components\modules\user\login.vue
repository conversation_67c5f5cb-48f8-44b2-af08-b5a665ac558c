<template>
  <div class="inner_login">
    <div class="login-cont">
      <div class="mb-2">
        <div class="info_heading">Sign in to your Account</div>
        <div class="info_text mt-1">Please use an organization email id only.</div>
      </div>
      <div>
        <b-button
          title="Google"
          v-b-tooltip.hover.top
          class="login-with-btn google"
          @click="loginWithSocial('google')"></b-button>
        <b-button
          title="Microsoft"
          v-b-tooltip.hover.top
          class="login-with-btn microsoft"
          @click="loginWithSocial('microsoft')"></b-button>
        <b-button
          title="Email"
          v-b-tooltip.hover.top
          class="login-with-btn email"
          @click="showEmailLogin = !showEmailLogin"></b-button>
      </div>
      <div v-if="showEmailLogin" style="width: 80%">
        <div class="info_text my-2">------ or, Sign in with Email ------</div>
        <div>
          <b-form>
            <b-input-group class="my-3">
              <b-input-group-prepend is-text>
                <i class="fa fa-envelope"></i>
              </b-input-group-prepend>
              <b-form-input
                autocomplete="on"
                id="email-input"
                placeholder="Enter your email"
                v-model="form.email"
                type="text"
                @blur="v$.form.email.$touch()"></b-form-input>
              <b-form-invalid-feedback :state="validateState('email')">
                Email is required
              </b-form-invalid-feedback>
            </b-input-group>
            <b-input-group class="my-3">
              <b-input-group-prepend is-text>
                <i class="fa fa-key"></i>
              </b-input-group-prepend>
              <b-form-input
                autocomplete="on"
                id="password-input"
                placeholder="Enter your password"
                v-model="form.password"
                :type="hide ? 'password' : 'text'"
                @blur="v$.form.password.$touch()"></b-form-input>
              <b-input-group-append is-text>
                <a @click="hide = !hide" class="color-unset">
                  <i class="fa" :class="{ 'fa-eye': !hide, 'fa-eye-slash': hide }"></i>
                </a>
              </b-input-group-append>
              <b-form-invalid-feedback :state="validateState('password')">
                Password is required
              </b-form-invalid-feedback>
            </b-input-group>
          </b-form>
          <div class="text-center">
            <b-button
              class="w-50 w-md-25 primaryBg font-weight-bold"
              @click="loginWithEmail()"
              :disabled="v$.form.$invalid || Isworking">
              <span v-if="!Isworking">Login</span>
              <span v-else>
                <div class="spinner-border" role="status"></div>
              </span>
            </b-button>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-around my-2 w-100">
        <router-link to="/register" class="font-weight-bold">Sign Up</router-link>
        <router-link to="/recovery" class="font-weight-bold">Forgot password?</router-link>
      </div>
      <div class="info_text mt-2">
        By clicking on <span class="font-weight-bolder">"login or sign up"</span> above, you
        acknowledge that you have read and understood, and agree to
        <span class="font-weight-bolder">Datasee.ai's</span><br />
        <b-link href="/login" class="font-weight-bolder">Terms & Conditions</b-link> and
        <b-link href="/login" class="font-weight-bolder">Privacy Policy</b-link>.
      </div>
    </div>
    <div class="logo-cont">
      <div class="my-2 w-100">
        <b-img :src="imageURL" rounded class="w-100"></b-img>
      </div>
      <div class="my-2 w-100">
        <div class="label-text mb-2 font-weight-bold">Trusted by Industry Leaders ...</div>
        <div class="client-logos">
          <b-img
            v-for="img in clientRows"
            :key="img"
            :src="`${clientimageURL + img}.png`"
            :alt="img"
            rounded></b-img>
        </div>
      </div>
      <div class="my-2 w-100">
        <div class="label-text mb-2 font-weight-bold">Software for Solar Projects in ...</div>
        <div class="client-logos" style="color: black">
          <div class="feature-text">
            <i class="fa-regular fa-circle-check"></i> <span>Development</span>
          </div>
          <div class="feature-text">
            <i class="fa-regular fa-circle-check"></i> <span>Construction</span>
          </div>
          <div class="feature-text">
            <i class="fa-regular fa-circle-check"></i> <span>Operations</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { encryptString } from '@/services/dataService';
import keycloakService from '@/services/keycloakService';
import { useVuelidate } from '@vuelidate/core';
import { email, required } from '@vuelidate/validators';

export default {
  name: 'login',
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      imageURL: process.env.VUE_APP_LOGO_LOGIN_PAGE,
      clientimageURL: process.env.VUE_APP_LOGO_CLIENT,
      clientRows: [
        'enel',
        'lnt',
        'acwa-power',
        'nevados',
        'statkraft',
        'gulfrel',
        'cleanmax',
        'azure-power',
        'mahindra-teqo',
        'alfanar',
        'plus-power',
        'juniper-green',
      ],
      showEmailLogin: false,
      hide: true,
      Isworking: false,
      form: {
        email: '',
        password: '',
      },
    };
  },
  validations() {
    return {
      form: {
        email: {
          required,
          email,
        },
        password: {
          required,
        },
      },
    };
  },
  methods: {
    validateState(name) {
      const field = this.v$.form[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    loginWithEmail() {
      this.Isworking = true;
      const loginObject = {
        username: encryptString(this.form.email.trim()),
        password: encryptString(this.form.password.trim()),
      };
      keycloakService
        .loginWithEmail(loginObject)
        .then(response => {
          this.$router.push('/home');
          this.$root.$emit('showToast', {
            message: 'Welcome!',
            title: response.message,
            variant: 'success',
          });
          this.Isworking = false;
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
          this.Isworking = false;
        });
    },
    loginWithSocial(provider) {
      keycloakService.loginWithSocial(provider);
    },
  },
  mounted() {
    this.$store.dispatch('logout');
  },
};
</script>

<style scoped>
.login-cont,
.logo-cont {
  max-width: 450px;
  width: 100%;
  display: flex;
  padding: 5px;
}
.info_heading,
.info_text {
  text-align: center;
  font-weight: 600;
}
.inner_login {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  gap: 1rem;
}
.login-cont {
  flex: 1 1 300px;
  padding: 1rem;
  box-shadow: 0 0 8px #b5b5b5;
  background: var(--transparent-white);
  flex-direction: column;
  align-items: center;
}
.logo-cont {
  flex: 1 1 300px;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
.info_heading {
  font-size: 24px;
  text-transform: uppercase;
}
.info_text {
  font-size: 12px;
  text-transform: capitalize;
  line-height: 1.5rem;
}
.login-with-btn {
  transition: background-color 0.3s, box-shadow 0.3s;
  border-radius: 5px !important;
  box-shadow: -1px 0 0 rgba(0, 0, 0, 0.04), 0 1px 1px rgba(0, 0, 0, 0.25) !important;
  color: #000;
  background-color: rgb(248 250 252) !important;
  background-repeat: no-repeat;
  background-position: 8px;
  margin: 0.5rem;
  padding: 1.3rem;
  border: none;
}
.login-with-btn:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}
.google {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjUiIGhlaWdodD0iMjUiIHZpZXdCb3g9IjAgMCA0OCA0OCI+CjxwYXRoIGZpbGw9IiNGRkMxMDciIGQ9Ik00My42MTEsMjAuMDgzSDQyVjIwSDI0djhoMTEuMzAzYy0xLjY0OSw0LjY1Ny02LjA4LDgtMTEuMzAzLDhjLTYuNjI3LDAtMTItNS4zNzMtMTItMTJjMC02LjYyNyw1LjM3My0xMiwxMi0xMmMzLjA1OSwwLDUuODQyLDEuMTU0LDcuOTYxLDMuMDM5bDUuNjU3LTUuNjU3QzM0LjA0Niw2LjA1MywyOS4yNjgsNCwyNCw0QzEyLjk1NSw0LDQsMTIuOTU1LDQsMjRjMCwxMS4wNDUsOC45NTUsMjAsMjAsMjBjMTEuMDQ1LDAsMjAtOC45NTUsMjAtMjBDNDQsMjIuNjU5LDQzLjg2MiwyMS4zNSw0My42MTEsMjAuMDgzeiI+PC9wYXRoPjxwYXRoIGZpbGw9IiNGRjNEMDAiIGQ9Ik02LjMwNiwxNC42OTFsNi41NzEsNC44MTlDMTQuNjU1LDE1LjEwOCwxOC45NjEsMTIsMjQsMTJjMy4wNTksMCw1Ljg0MiwxLjE1NCw3Ljk2MSwzLjAzOWw1LjY1Ny01LjY1N0MzNC4wNDYsNi4wNTMsMjkuMjY4LDQsMjQsNEMxNi4zMTgsNCw5LjY1Niw4LjMzNyw2LjMwNiwxNC42OTF6Ij48L3BhdGg+PHBhdGggZmlsbD0iIzRDQUY1MCIgZD0iTTI0LDQ0YzUuMTY2LDAsOS44Ni0xLjk3NywxMy40MDktNS4xOTJsLTYuMTktNS4yMzhDMjkuMjExLDM1LjA5MSwyNi43MTUsMzYsMjQsMzZjLTUuMjAyLDAtOS42MTktMy4zMTctMTEuMjgzLTcuOTQ2bC02LjUyMiw1LjAyNUM5LjUwNSwzOS41NTYsMTYuMjI3LDQ0LDI0LDQ0eiI+PC9wYXRoPjxwYXRoIGZpbGw9IiMxOTc2RDIiIGQ9Ik00My42MTEsMjAuMDgzSDQyVjIwSDI0djhoMTEuMzAzYy0wLjc5MiwyLjIzNy0yLjIzMSw0LjE2Ni00LjA4Nyw1LjU3MWMwLjAwMS0wLjAwMSwwLjAwMi0wLjAwMSwwLjAwMy0wLjAwMmw2LjE5LDUuMjM4QzM2Ljk3MSwzOS4yMDUsNDQsMzQsNDQsMjRDNDQsMjIuNjU5LDQzLjg2MiwyMS4zNSw0My42MTEsMjAuMDgzeiI+PC9wYXRoPgo8L3N2Zz4=);
}
.microsoft {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMjUiIGhlaWdodD0iMjUiIHZpZXdCb3g9IjAgMCA0OCA0OCI+CjxwYXRoIGZpbGw9IiNmZjU3MjIiIGQ9Ik02IDZIMjJWMjJINnoiIHRyYW5zZm9ybT0icm90YXRlKC0xODAgMTQgMTQpIj48L3BhdGg+PHBhdGggZmlsbD0iIzRjYWY1MCIgZD0iTTI2IDZINDJWMjJIMjZ6IiB0cmFuc2Zvcm09InJvdGF0ZSgtMTgwIDM0IDE0KSI+PC9wYXRoPjxwYXRoIGZpbGw9IiNmZmMxMDciIGQ9Ik0yNiAyNkg0MlY0MkgyNnoiIHRyYW5zZm9ybT0icm90YXRlKC0xODAgMzQgMzQpIj48L3BhdGg+PHBhdGggZmlsbD0iIzAzYTlmNCIgZD0iTTYgMjZIMjJWNDJINnoiIHRyYW5zZm9ybT0icm90YXRlKC0xODAgMTQgMzQpIj48L3BhdGg+Cjwvc3ZnPg==);
}
.email {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAACXBIWXMAAAsTAAALEwEAmpwYAAACUklEQVR4nOVUS4iNYRh+aIiQS0nIQiliIVmwoFw22LiVS41bsmDlUkrkTzmd8z3v9805OLOYhVmwtGFkFBuZpJiFWKDUZEFRFIXJTI7+73/P5ytzLqZjIW/99f/P/77P872X7wX+PysUpsLa9SDbIbIPxmyCc3NbQ27tcpA3QH6HSOW3h3wEcvPoyCuVMSDPgRxWwhcQyYM8BJE9IE9C5Hb4T3YjSdr+TIQsKflniOxAkowd0a9QWALyqQp1Ni8gskuDPsC5pQ398/npEBnwMdauaiyQJBMg8kZF9kd4G8jDIPtAPvelSjNMy5plvlUzv95MFgdVoD8QdHWNA9lTo/HHo0O8AznoD9pA5K4GH42wM0r63o9xlu09xY5Efre0ZMvqi5CfNHi+Bk4KGNke+T1RbHuEdWrsutoCpdIsDRwM0/RrCL4iScZHhG89bszqKJOiimyrLeLcSiV8GZFZDXwcsPQAIkMqsjDy7VbfjbVFRFaoyLMIu6pYT8ByuZmh8cXitEikz2POLagnsliDX0XYZRV5EDBrtyg2HCbQmDl6+wdQ1zo6Zofg6hiKnFDhIRiz0/dA5LX6/YC1i1AuTwbZq37H6otkpNWLuMF/OzcjNFnCk97u++qXLs5v+t7b3P4SKSvRzYClNSavKPFFP4Xp6icvgHwI8o6/L00vyIywutYP4K+ZyKnQG5GzcG7iiH7GTBm9SDoxIud9Y7OMPoK8BjIH8jTIS1q6L433VCNL13a2bYdqLMd+P5EtsbTJxqyFtbtB7oXIGlg7rzXk/5L9BE3tnlvke1aeAAAAAElFTkSuQmCC);
}
.feature-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}
@media (max-width: 767px) {
  .inner_login {
    flex-direction: column;
    align-items: center;
  }
  .login-cont,
  .logo-cont {
    max-width: 100%;
    width: 100%;
  }
  .info_heading {
    font-size: 20px;
  }
  .feature-text,
  .label-text {
    font-size: 13px;
  }
  .label-text {
    text-align: center;
  }
  .login-with-btn {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
}
.client-logos {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem 2rem;
  row-gap: 0.75rem;
}
.client-logos img {
  width: clamp(60px, 20%, 100px);
  height: auto;
  object-fit: contain;
  transition: transform 0.2s ease-in-out;
}
.client-logos img:hover {
  transform: scale(1.05);
}
@media (min-width: 768px) {
  .client-logos img {
    width: 30%;
    max-width: 120px;
  }
}
</style>
