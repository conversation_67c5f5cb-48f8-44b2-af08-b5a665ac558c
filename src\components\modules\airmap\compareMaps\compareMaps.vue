<template>
  <div class="map-layout">
    <div ref="myMap" class="w-100 h-100"></div>
    <div class="dates-filter">
      <b-row>
        <b-col cols="6">
          <b-form-select
            class="date-select"
            :options="datesArray"
            :value="leftDate"
            @change="getCompareData($event, 'left')">
          </b-form-select>
        </b-col>
        <b-col cols="6">
          <b-form-select
            class="date-select"
            :options="datesArray"
            :value="rightDate"
            @change="getCompareData($event, 'right')">
          </b-form-select>
        </b-col>
      </b-row>
    </div>
    <div class="compare-page-btns">
      <b-button @click="$router.go(-1)" title="Back to Map" v-b-tooltip.hover.right>
        <i class="fas fa-arrow-left"></i>
      </b-button>
      <b-button @click="showCompareDetails('Modal')" v-b-tooltip.hover.left title="Summary">
        <i class="icon fa fa-bar-chart"></i>
      </b-button>
      <b-button
        @click="showCompareDetails('ModalInv')"
        v-b-tooltip.hover.left
        title="Sub Camp Wise">
        <i class="icon fas fa-chart-line"></i>
      </b-button>
      <b-button
        @click="showCompareDetails('ModalInvE')"
        v-b-tooltip.hover.left
        title="Feature Wise">
        <i class="fas fa-chart-area icon"></i>
      </b-button>
    </div>
    <div class="satellite-view">
      <b-button
        class="btn-primary shadow"
        @click="toggleMapLayer()"
        v-b-tooltip.hover.left
        title="Toggle Map Layer">
        <i class="fa fa-satellite" v-if="terrain"></i>
        <i class="fa fa-mountain-sun" v-else></i>
      </b-button>
    </div>
    <div class="full-screen">
      <b-button
        v-b-tooltip.hover.left
        title="Full Screen"
        class="btn-primary"
        @click="toggleFullscreen()">
        <i :class="fullscreenIcon"></i>
      </b-button>
    </div>
    <div class="reset-map">
      <b-button
        v-b-tooltip.hover.left
        title="Reset Map"
        class="btn-primary"
        @click="resetMapView()">
        <i class="fa fa-arrows-alt"></i>
      </b-button>
    </div>
    <b-modal
      size="lg"
      id="openModal"
      title="Comparison"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <div v-if="projectType === isConstruction && modalId === 'compareDetailsModal'">
        <compareDetails />
      </div>
      <div v-else-if="modalId === 'Modal'">
        <compareDetailsSolar />
      </div>
      <div v-else-if="modalId === 'ModalInv'">
        <compareDetailsSolarInv />
      </div>
      <div v-else-if="modalId === 'ModalInvE'">
        <compareDetailsSolarInvE />
      </div>
    </b-modal>
  </div>
</template>

<script>
import '@/assets/plugins/leaflet-edgebuffer';
import L from 'leaflet';
import 'leaflet-side-by-side';
import { toRaw } from 'vue';

const compareDetails = () => import('./compareDetails.vue');
const compareDetailsSolar = () => import('./compareDetailsSolar.vue');
const compareDetailsSolarInv = () => import('./compareDetailsSolarInv.vue');
const compareDetailsSolarInvE = () => import('./compareDetailsSolarInvE.vue');

export default {
  name: 'compareMaps',
  components: {
    compareDetails,
    compareDetailsSolar,
    compareDetailsSolarInv,
    compareDetailsSolarInvE,
  },
  data() {
    return {
      terrainUrl: process.env.VUE_APP_TERRAIN_TILE_LAYER,
      satelliteUrl: process.env.VUE_APP_SATTELITE_TILE_LAYER,
      map: null,
      compareControl: null,
      leftRgbLayer: null,
      rightRgbLayer: null,
      terrain: true,
      fullscreen: false,
      modalId: '',
      options: {
        attributionControl: false,
        zoomControl: false,
        tapHold: false,
        minZoom: 2,
        maxZoom: 23,
        zoomSnap: 0.5,
        maxBoundsViscosity: 1.0,
        maxBounds: [
          [-90, -180],
          [90, 180],
        ],
      },
      tileOptions: {
        minZoom: 2,
        maxZoom: 23,
        edgeBufferTiles: 1,
      },
    };
  },
  computed: {
    projectType() {
      return this.$store.state.homeStore.projectData.currentDateProject.type;
    },
    center() {
      const center = this.$store.state.homeStore.projectData.center;
      if (!center) return [0, 0];
      const [lat, lng] = center.split(',').map(coord => parseFloat(coord.trim()));
      return !isNaN(lat) && !isNaN(lng) ? [lat, lng] : [0, 0];
    },
    zoom() {
      return this.$store.state.homeStore.projectData.zoom_level || 15;
    },
    datesArray() {
      return this.$store.state.homeStore.projectData.date
        .filter(x => x.type === this.projectType)
        .map(x => x.date);
    },
    leftDate() {
      return this.$store.state.homeStore.compareDataLeft.date;
    },
    rightDate() {
      return this.$store.state.homeStore.compareDataRight.date;
    },
    rgbUrlLeft() {
      const url = this.$store.state.homeStore.compareDataLeft.properties.ortho.RGB;
      return `${url}{z}/{x}/{y}.png` || '';
    },
    rgbUrlRight() {
      const url = this.$store.state.homeStore.compareDataRight.properties.ortho.RGB;
      return `${url}{z}/{x}/{y}.png` || '';
    },
    fullscreenIcon() {
      return this.fullscreen
        ? 'fa fa-down-left-and-up-right-to-center'
        : 'fa fa-up-right-and-down-left-from-center';
    },
  },
  methods: {
    initializeMap() {
      this.map = L.map(this.$refs.myMap, {
        ...this.options,
        center: this.center,
        zoom: this.zoom,
      });
      const rawMap = toRaw(this.map);

      L.control.scale({ position: 'bottomright', imperial: true, metric: true }).addTo(rawMap);
      L.control.zoom({ position: 'bottomright' }).addTo(rawMap);

      this.currentTileLayer = L.tileLayer(this.terrain ? this.terrainUrl : this.satelliteUrl, {
        ...this.tileOptions,
      }).addTo(rawMap);

      this.leftRgbLayer = L.tileLayer(this.rgbUrlLeft, {
        ...this.tileOptions,
      }).addTo(rawMap);
      this.rightRgbLayer = L.tileLayer(this.rgbUrlRight, {
        ...this.tileOptions,
      }).addTo(rawMap);

      this.compareControl = L.control
        .sideBySide(this.leftRgbLayer, this.rightRgbLayer)
        .addTo(rawMap);
    },
    toggleMapLayer() {
      this.terrain = !this.terrain;
      if (this.currentTileLayer) {
        this.map.removeLayer(this.currentTileLayer);
      }
      this.currentTileLayer = L.tileLayer(this.terrain ? this.terrainUrl : this.satelliteUrl, {
        ...this.tileOptions,
      }).addTo(toRaw(this.map));

      this.leftRgbLayer.setZIndex(1000);
      this.rightRgbLayer.setZIndex(1000);
    },
    getCompareData(date, side) {
      this.$store.dispatch('homeStore/setCompareData', { date, side });
      const rgbUrl = side === 'left' ? this.rgbUrlLeft : this.rgbUrlRight;
      const layer = side === 'left' ? this.leftRgbLayer : this.rightRgbLayer;
      if (layer && rgbUrl) {
        layer.setUrl(rgbUrl);
      }
    },
    showCompareDetails(modalId) {
      this.modalId = modalId;
      this.$bvModal.show('openModal');
    },
    toggleFullscreen() {
      try {
        const elem = document.documentElement;
        this.fullscreen = !this.fullscreen;

        if (this.fullscreen) {
          if (elem.requestFullscreen) elem.requestFullscreen();
          else if (elem.webkitRequestFullscreen) elem.webkitRequestFullscreen();
          else if (elem.msRequestFullscreen) elem.msRequestFullscreen();
        } else {
          if (document.exitFullscreen) document.exitFullscreen();
          else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
          else if (document.msExitFullscreen) document.msExitFullscreen();
        }
      } catch (error) {
        this.$root.$emit('showToast', {
          message: 'Try again later or contact support',
          title: error.message,
          variant: 'danger',
        });
      }
    },
    resetMapView() {
      if (this.map) this.map.flyTo(this.center, this.zoom);
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initializeMap();
    });
  },
  beforeUnmount() {
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  },
};
</script>

<style scoped>
.map-layout {
  height: 100vh;
}
.full-screen {
  position: absolute;
  bottom: 8.8rem;
  right: 7.5px;
  z-index: 1000;
}
.reset-map {
  position: absolute;
  bottom: 0.7rem;
  right: 7.5px;
  z-index: 1000;
}
.satellite-view {
  position: absolute;
  bottom: 11.5rem;
  right: 7.5px;
  z-index: 1000;
}
</style>
