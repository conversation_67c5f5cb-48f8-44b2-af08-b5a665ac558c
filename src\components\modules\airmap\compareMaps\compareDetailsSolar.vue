<template>
  <div class="compareDetailsContainer">
    <div class="d-flex justify-content-between mb-2">
      <b-form-select
        v-if="projectType == 'SCPM'"
        class="w-25 width-set"
        v-model="selectedAsset"
        :options="assetnames"
        value="selectedAsset"></b-form-select>
      <VueToggles
        @click="isChart = !isChart"
        :value="isChart"
        height="25"
        width="80"
        checkedText="Chart"
        uncheckedText="Table"
        checkedBg="darkcyan"
        uncheckedBg="darkcyan"
        fontSize="20px"
        fontWeight="500" />
    </div>
    <div v-if="isChart">
      <b-row>
        <b-col cols="12">
          <div class="chartContainer mx-auto">
            <apexchart type="bar" :options="options" :series="series"></apexchart>
          </div>
        </b-col>
      </b-row>
    </div>
    <div v-else>
      <b-table
        class="table-style"
        bordered
        striped
        responsive
        hover
        head-variant="dark"
        :items="paginatedData"
        :fields="fields"></b-table>
      <b-pagination
        class="justify-content-center m-0"
        v-model="currentPage"
        :total-rows="tableData.length"
        :per-page="perPage"
        first-text="First"
        prev-text="Prev"
        next-text="Next"
        last-text="Last"
        pills></b-pagination>
    </div>
  </div>
</template>

<script>
import { SOLAR_TYPE } from '@/services/constant';
import { mapState } from 'vuex';

export default {
  name: 'compareDetailsSolar',
  data() {
    return {
      selectedAsset: 'Structures',
      isChart: false,
      assetnames: [],
      perPage: 5, // Number of items per page
      currentPage: 1, // Current page
    };
  },
  computed: {
    ...mapState({
      projectType: state => state.homeStore.projectData.currentDateProject.type,
      leftDate: state => state.homeStore.compareDataLeft.date,
      rightDate: state => state.homeStore.compareDataRight.date,
      summaryLeft: state => state.homeStore.compareDataLeft?.summary?.summary ?? {},
      summaryRight: state => state.homeStore.compareDataRight?.summary?.summary ?? {},
      deviationLeft: state => state.homeStore.compareDataLeft?.deviation?.data?.Summary ?? {},
      deviationRight: state => state.homeStore.compareDataRight?.deviation?.data?.Summary ?? {},
    }),
    paginatedData() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.tableData.slice(start, end);
    },
    summaryLeftTransformed() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        return this.objectToArraySCPM(this.summaryLeft);
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        return this.objectToArraySCQM(this.deviationLeft);
      }
      return [];
    },
    summaryLeftData() {
      return this.summaryLeftTransformed.map(asset => asset.current_value) ?? [];
    },
    summaryRightTransformed() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        return this.objectToArraySCPM(this.summaryRight);
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        return this.objectToArraySCQM(this.deviationRight);
      }
      return [];
    },
    summaryRightData() {
      return this.summaryRightTransformed.map(asset => asset.current_value) ?? [];
    },
    shownAssets() {
      const shownAssets = {
        names: this.summaryRightTransformed
          .slice(this.start, this.end)
          .map(asset => asset.assetName),
        leftData: this.summaryLeftData.slice(this.start, this.end),
        rightData: this.summaryRightData.slice(this.start, this.end),
      };
      return shownAssets;
    },
    options() {
      return {
        chart: {
          id: 'vuechart-compare',
        },
        xaxis: {
          categories: this.shownAssets.names,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-xaxis-label',
            },
          },
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-yaxis-label',
            },
          },
        },
        plotOptions: {
          bar: {
            horizontal: false,
            dataLabels: {
              position: 'bottom',
            },
          },
        },
        legend: {
          fontSize: '20px',
          fontFamily: 'Helvetica, Arial',
          fontWeight: 400,
          itemMargin: {
            horizontal: 10,
            vertical: 5,
          },
        },
        dataLabels: {
          enabled: true,
          enabledOnSeries: undefined,
          textAnchor: 'middle',
          distributed: false,
          offsetX: 0,
          offsetY: 0,
          style: {
            fontSize: '14px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            fontWeight: 'bold',
            colors: ['#fff'],
          },
          background: {
            enabled: true,
            foreColor: '#000',
            padding: 4,
            borderRadius: 2,
            borderWidth: 1,
            borderColor: '#fff',
            opacity: 0.9,
            dropShadow: {
              enabled: false,
              top: 1,
              left: 1,
              blur: 1,
              color: '#000',
              opacity: 0.45,
            },
          },
          dropShadow: {
            enabled: false,
            top: 1,
            left: 1,
            blur: 1,
            color: '#000',
            opacity: 0.45,
          },
        },
        responsive: [
          {
            breakpoint: 995,
            xaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  fontFamily: 'Helvetica, Arial, sans-serif',
                  fontWeight: 400,
                  cssClass: 'apexcharts-xaxis-label',
                },
              },
            },
            yaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  fontFamily: 'Helvetica, Arial, sans-serif',
                  fontWeight: 400,
                  cssClass: 'apexcharts-yaxis-label',
                },
              },
            },
            options: {
              plotOptions: {
                bar: {
                  horizontal: true,
                },
              },
              legend: {
                position: 'right',
                verticalAlign: 'top',
                fontSize: '14px',
                fontWeight: 500,
                itemMargin: {
                  horizontal: 10,
                  vertical: 5,
                },
              },
              dataLabels: {
                enabled: false,
              },
            },
          },
        ],
      };
    },
    series() {
      return [
        {
          name: this.leftDate,
          data: this.shownAssets.leftData,
        },
        {
          name: this.rightDate,
          data: this.shownAssets.rightData,
        },
      ];
    },
    tableData() {
      const tableData = this.summaryRightTransformed.map((asset, i) => {
        const leftData = parseInt(this.summaryLeftData[i] || 0);
        const rightData = parseInt(this.summaryRightData[i] || 0);
        const progress =
          leftData > rightData
            ? ((leftData - rightData) / leftData) * 100
            : ((rightData - leftData) / rightData) * 100;
        const progressValue = isNaN(progress) ? 0.0 : progress;
        return {
          Asset: asset.assetName,
          'Left Data': leftData,
          'Right Data': rightData,
          Progress: progressValue.toFixed(2),
        };
      });
      return tableData;
    },
    fields() {
      const fieldData = [
        { key: 'Asset', label: this.selectedAsset, sortable: true },
        { key: 'Left Data', label: this.leftDate, sortable: true },
        { key: 'Right Data', label: this.rightDate, sortable: true },
        { key: 'Progress', label: 'Progress (%)', sortable: true },
      ];
      return fieldData;
    },
  },
  methods: {
    objectToArraySCQM(obj) {
      let requiredArray = [];
      for (let prop in obj) {
        requiredArray.push({
          assetName: prop,
          current_value: obj[prop].actual,
        });
      }
      return requiredArray;
    },
    objectToArraySCPM(obj) {
      let requiredArray = [];
      for (const propL1 in obj) {
        for (const propL2 in obj[propL1]) {
          if (!this.assetnames.includes(propL1)) this.assetnames.push(propL1);
          if (propL1 == this.selectedAsset) {
            if (propL1 == 'Lightning Arresters') {
              requiredArray.push({
                assetName: 'LA - ' + `${propL2}`,
                current_value: obj[propL1][propL2].Actual,
              });
            } else {
              requiredArray.push({
                assetName: `${propL2}`,
                current_value: obj[propL1][propL2].Actual,
              });
            }
          }
        }
      }
      return requiredArray;
    },
  },
};
</script>

<style scoped>
.compareDetailsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
