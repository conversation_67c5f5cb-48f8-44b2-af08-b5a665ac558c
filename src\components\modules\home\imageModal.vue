<template>
  <div>
    <b-tabs card fill justified active-nav-item-class="font-weight-bold text-primary">
      <b-tab>
        <template #title> <i class="fas fa-camera-retro mr-2"></i> Inner Obliques </template>
        <div v-if="hasData(inner_obliques)">
          <b-tabs card pills vertical>
            <b-tab
              v-for="(images, key, index) in inner_obliques"
              :key="'inner-' + key"
              :title="key"
              :active="index === 0">
              <b-carousel fade controls indicators>
                <b-carousel-slide v-for="(image, i) in images" :key="'inner-img-' + i">
                  <template #img>
                    <div class="mx-5">
                      <b-img
                        :src="image"
                        :alt="`Inner Oblique ${key} - Image ${i}`"
                        rounded
                        thumbnail
                        fluid></b-img>
                    </div>
                  </template>
                </b-carousel-slide>
              </b-carousel>
            </b-tab>
          </b-tabs>
        </div>
        <div v-else class="text-center font-weight-bolder m-0 font-size-24">
          Images not available for the date selected.
        </div>
      </b-tab>
      <b-tab>
        <template #title> <i class="fas fa-film mr-2"></i> Outer Obliques </template>
        <div v-if="hasData(outer_obliques)">
          <b-tabs card pills vertical>
            <b-tab
              v-for="(images, key, index) in outer_obliques"
              :key="'outer-' + key"
              :title="key"
              :active="index === 0">
              <b-carousel fade controls indicators>
                <b-carousel-slide v-for="(image, i) in images" :key="'outer-img-' + i">
                  <template #img>
                    <div class="mx-5">
                      <b-img
                        :src="image"
                        :alt="`Outer Oblique ${key} - Image ${i}`"
                        rounded
                        thumbnail
                        fluid></b-img>
                    </div>
                  </template>
                </b-carousel-slide>
              </b-carousel>
            </b-tab>
          </b-tabs>
        </div>
        <div v-else class="text-center font-weight-bolder m-0 font-size-24">
          Images not available for the date selected.
        </div>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
export default {
  name: 'imageModal',
  props: {
    inner_obliques: {
      type: Object,
      required: true,
    },
    outer_obliques: {
      type: Object,
      required: true,
    },
  },
  methods: {
    hasData(data) {
      return Object.keys(data).some(key => Array.isArray(data[key]) && data[key].length > 0);
    },
  },
};
</script>

<style scoped></style>
