<template>
  <div>
    <navbar />
    <b-container class="p-3">
      <b-tabs card fill justified active-nav-item-class="font-weight-bold text-primary">
        <b-tab active>
          <template #title><i class="fa-solid fa-user"></i> Manage Profile</template>
          <b-form>
            <b-row>
              <b-col cols="12" md="4">
                <b-form-group label="Company:" label-for="company-input">
                  <b-form-input class="w-100" id="company-input" v-model="companyName" disabled />
                </b-form-group>
              </b-col>
              <b-col cols="12" md="4">
                <b-form-group label="Email:" label-for="email-input">
                  <b-form-input class="w-100" id="email-input" v-model="userForm.email" disabled />
                </b-form-group>
              </b-col>
              <b-col cols="12" md="4">
                <b-form-group label="Contact:" label-for="contact-input">
                  <b-form-input
                    class="w-100"
                    id="contact-input"
                    v-model="userForm.contact"
                    type="number"
                    placeholder="Enter your contact"
                    @blur="v$.userForm.contact.$touch()" />
                  <b-form-invalid-feedback :state="validateState('contact')">
                    Must be at least 10 numbers
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
            </b-row>
            <b-row>
              <b-col cols="12" md="4">
                <b-form-group label="First Name:" label-for="firstname-input">
                  <b-form-input
                    class="w-100"
                    id="firstname-input"
                    v-model="userForm.first_name"
                    placeholder="Enter your first name"
                    @blur="v$.userForm.first_name.$touch()" />
                  <b-form-invalid-feedback :state="validateState('first_name')">
                    First Name must be at least 3 characters long
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
              <b-col cols="12" md="4">
                <b-form-group label="Last Name:" label-for="lastname-input">
                  <b-form-input
                    class="w-100"
                    id="lastname-input"
                    v-model="userForm.last_name"
                    placeholder="Enter your last name"
                    @blur="v$.userForm.last_name.$touch()" />
                  <b-form-invalid-feedback :state="validateState('last_name')">
                    Last Name must be at least 3 characters long
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
              <b-col cols="12" md="4">
                <b-form-group label="Pincode:" label-for="pincode-input">
                  <b-form-input
                    class="w-100"
                    id="pincode-input"
                    v-model="userForm.pincode"
                    type="number"
                    placeholder="Enter your pincode"
                    @blur="v$.userForm.pincode.$touch()" />
                  <b-form-invalid-feedback :state="validateState('pincode')">
                    Must be 6 numbers
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
            </b-row>
            <b-row>
              <b-col cols="12" md="4">
                <b-form-group label="Country:" label-for="country-input">
                  <b-form-input
                    class="w-100"
                    id="country-input"
                    v-model="userForm.country"
                    placeholder="Enter your country"
                    @blur="v$.userForm.country.$touch()" />
                  <b-form-invalid-feedback :state="validateState('country')">
                    Country is required
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
              <b-col cols="12" md="4">
                <b-form-group label="State:" label-for="state-input">
                  <b-form-input
                    class="w-100"
                    id="state-input"
                    v-model="userForm.state"
                    placeholder="Enter your state"
                    @blur="v$.userForm.state.$touch()" />
                  <b-form-invalid-feedback :state="validateState('state')">
                    State is required
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
              <b-col cols="12" md="4">
                <b-form-group label="City:" label-for="city-input">
                  <b-form-input
                    class="w-100"
                    id="city-input"
                    v-model="userForm.city"
                    placeholder="Enter your city"
                    @blur="v$.userForm.city.$touch()" />
                  <b-form-invalid-feedback :state="validateState('city')">
                    City is required
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
            </b-row>
            <div class="text-center mt-3">
              <b-button class="primaryBg" @click="updateProfile()" :disabled="v$.userForm.$invalid">
                Submit
              </b-button>
            </div>
          </b-form>
        </b-tab>
        <b-tab>
          <template #title><i class="fa-solid fa-key"></i> Change Password</template>
          <b-form>
            <b-row>
              <b-col cols="12" md="6">
                <b-form-group label="Password:" label-for="password-input">
                  <b-input-group>
                    <b-form-input
                      id="password-input"
                      placeholder="Enter your new password"
                      v-model="passwordForm.password"
                      :type="hide ? 'password' : 'text'"
                      @blur="v$.passwordForm.password.$touch()" />
                    <b-input-group-append is-text>
                      <a @click="hide = !hide" class="color-unset">
                        <i class="fa" :class="{ 'fa-eye': !hide, 'fa-eye-slash': hide }"></i>
                      </a>
                    </b-input-group-append>
                  </b-input-group>
                  <b-form-invalid-feedback :state="validateStatePassword('password')">
                    Password is required and must be at least 6 characters
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
              <b-col cols="12" md="6">
                <b-form-group label="Confirm Password:" label-for="confirmPassword-input">
                  <b-input-group>
                    <b-form-input
                      id="confirmPassword-input"
                      placeholder="Confirm your password"
                      v-model="passwordForm.confirmPassword"
                      :type="hide1 ? 'password' : 'text'"
                      @blur="v$.passwordForm.confirmPassword.$touch()" />
                    <b-input-group-append is-text>
                      <a @click="hide1 = !hide1" class="color-unset">
                        <i class="fa" :class="{ 'fa-eye': !hide1, 'fa-eye-slash': hide1 }"></i>
                      </a>
                    </b-input-group-append>
                  </b-input-group>
                  <b-form-invalid-feedback :state="validateStatePassword('confirmPassword')">
                    Passwords do not match
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
            </b-row>
            <div class="text-center mt-3">
              <b-button
                class="primaryBg"
                @click="changePassword()"
                :disabled="v$.passwordForm.$invalid">
                Submit
              </b-button>
            </div>
          </b-form>
        </b-tab>
      </b-tabs>
    </b-container>
  </div>
</template>

<script>
import { useVuelidate } from '@vuelidate/core';
import { email, maxLength, minLength, numeric, required, sameAs } from '@vuelidate/validators';
const navbar = () => import('@/components/shared/navbar.vue');

export default {
  name: 'userProfile',
  components: {
    navbar,
  },
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      userForm: {
        email: '',
        first_name: '',
        last_name: '',
        company: { name: '' },
        contact: '',
        pincode: '',
        country: '',
        state: '',
        city: '',
      },
      passwordForm: {
        password: '',
        confirmPassword: '',
      },
      hide: true,
      hide1: true,
    };
  },
  watch: {
    userDetails: {
      handler(newVal) {
        if (newVal) {
          this.userForm = { ...newVal };
        }
      },
      immediate: true,
    },
  },
  validations() {
    return {
      userForm: {
        email: {
          required,
          email,
        },
        first_name: {
          required,
        },
        last_name: {
          required,
        },
        company: {
          required,
        },
        contact: {
          required,
          numeric,
          minLength: minLength(10),
        },
        pincode: {
          required,
          numeric,
          minLength: minLength(6),
          maxLength: maxLength(6),
        },
        country: {
          required,
        },
        state: {
          required,
        },
        city: {
          required,
        },
      },
      passwordForm: {
        password: {
          required,
          minLength: minLength(6),
        },
        confirmPassword: { required, sameAsPassword: sameAs(this.passwordForm.password) },
      },
    };
  },
  computed: {
    userDetails() {
      return this.$store.state.userStore.loggedInUser;
    },
    companyName: {
      get() {
        return this.userForm?.company?.name || '';
      },
      set(value) {
        if (this.userForm && this.userForm.company) {
          this.userForm.company.name = value;
        }
      },
    },
  },
  methods: {
    validateState(name) {
      const field = this.v$.userForm[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    validateStatePassword(name) {
      const field = this.v$.passwordForm[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    updateProfile() {
      this.$store
        .dispatch('userStore/updateUserDetails', this.userForm)
        .then(response => {
          this.$root.$emit('showToast', {
            message: `User Profile Updated Successfully`,
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
        });
    },
    changePassword() {
      const data = {
        password: this.passwordForm.password,
        type: 'update',
        id: this.userForm.id,
      };
      this.$store
        .dispatch('userStore/resetPassword', data)
        .then(response => {
          this.passwordForm.password = '';
          this.passwordForm.confirmPassword = '';
          this.$root.$emit('showToast', {
            message: `Password Changed Successfully`,
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
        });
    },
  },
  mounted() {
    this.$store.dispatch('userStore/getUserDetails', false).catch(error => {
      this.$root.$emit('showToast', {
        title: error.message,
        message: 'There has been a problem in fetching user data',
        variant: 'danger',
      });
    });
  },
};
</script>

<style scoped>
@media (max-width: 767px) {
  :deep(.nav-tabs.nav-link) {
    padding: 8px 4px;
    font-size: 14px;
    text-align: center;
  }
  :deep(.nav-tabs.nav-linki) {
    display: block;
    margin-bottom: 4px;
    margin-right: 0 !important;
  }
  .input-group-text {
    padding: 8px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
