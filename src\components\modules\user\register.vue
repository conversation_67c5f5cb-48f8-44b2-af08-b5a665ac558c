<template>
  <div class="inner_login">
    <b-card class="shadow mx-auto w-100 max-width-500" v-if="!isEmailValid">
      <div class="text-center text-primary">
        <i class="fa fa-envelope fa-4x"></i>
        <p class="my-2 h4 font-weight-bold">Email Verification</p>
      </div>
      <p class="my-2 text-center font-weight-bold">*Note: Only Corporate Email ID are allowed.</p>
      <b-form class="w-100">
        <b-form-group class="my-4">
          <b-form-input
            class="w-100"
            v-model="userForm.email"
            type="email"
            placeholder="Enter your email"
            @blur="v$.userForm.email.$touch()"></b-form-input>
          <b-form-invalid-feedback :state="validateState('email')">
            Enter a valid email address
          </b-form-invalid-feedback>
        </b-form-group>
        <div class="d-flex justify-content-center align-items-center">
          <b-button
            class="primaryBg mr-4"
            @click="validateEmail"
            :disabled="v$.userForm.email.$invalid">
            Submit
          </b-button>
          <p class="m-0 font-weight-bold">Back to <router-link to="/login">Login</router-link></p>
        </div>
      </b-form>
    </b-card>
    <b-card class="container shadow mx-auto w-100" v-else>
      <div class="text-center text-primary mb-3">
        <i class="fa fa-address-card fa-4x"></i>
        <p class="my-2 h4 font-weight-bold">User Registration</p>
      </div>
      <b-form class="w-100">
        <b-row>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="Email:" label-for="email-input">
              <b-form-input
                class="w-100"
                id="email-input"
                v-model="userForm.email"
                type="email"
                disabled></b-form-input>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="Contact:" label-for="contact-input">
              <b-form-input
                class="w-100"
                id="contact-input"
                v-model="userForm.contact"
                type="number"
                placeholder="Enter your contact"
                @blur="v$.userForm.contact.$touch()"></b-form-input>
              <b-form-invalid-feedback :state="validateState('contact')">
                Must be at least 10 numbers
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="Organization:" label-for="organization-input">
              <b-form-input
                class="w-100"
                id="organization-input"
                v-model="userForm.organization"
                type="text"
                placeholder="Enter your organization"
                @blur="v$.userForm.organization.$touch()"></b-form-input>
              <b-form-invalid-feedback :state="validateState('organization')">
                Organization must be at least 6 characters long.
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="First Name:" label-for="first_name-input">
              <b-form-input
                class="w-100"
                id="first_name-input"
                v-model="userForm.first_name"
                type="text"
                placeholder="Enter your first name"
                @blur="v$.userForm.first_name.$touch()"></b-form-input>
              <b-form-invalid-feedback :state="validateState('first_name')">
                First Name must be at least 6 characters long.
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="Last Name:" label-for="last_name-input">
              <b-form-input
                class="w-100"
                id="last_name-input"
                v-model="userForm.last_name"
                type="text"
                placeholder="Enter your last name"
                @blur="v$.userForm.last_name.$touch()"></b-form-input>
              <b-form-invalid-feedback :state="validateState('last_name')">
                Last Name must be at least 6 characters long.
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="Pincode:" label-for="pincode-input">
              <b-form-input
                class="w-100"
                id="pincode-input"
                v-model="userForm.pincode"
                type="number"
                placeholder="Enter your pincode"
                @blur="v$.userForm.pincode.$touch()"></b-form-input>
              <b-form-invalid-feedback :state="validateState('pincode')">
                Must be 6 numbers
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="Country:" label-for="country-input">
              <b-form-select
                id="country-input"
                v-model="userForm.country"
                :options="
                  [{ text: 'Please select a country', value: '', disabled: true }].concat(
                    countriesList
                  )
                "
                @change="getStatesList"
                @blur="v$.userForm.country.$touch()"></b-form-select>
              <b-form-invalid-feedback :state="validateState('country')">
                Please select a country
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="State:" label-for="state-input">
              <b-form-select
                id="state-input"
                v-model="userForm.state"
                :disabled="v$.userForm.country.$invalid"
                :options="
                  [{ text: 'Please select a state', value: '', disabled: true }].concat(statesList)
                "
                @change="getCitiesList"
                @blur="v$.userForm.state.$touch()"></b-form-select>
              <b-form-invalid-feedback :state="validateState('state')">
                Please select a state
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group label="City:" label-for="city-input">
              <b-form-select
                id="city-input"
                v-model="userForm.city"
                :disabled="v$.userForm.state.$invalid"
                :options="
                  [{ text: 'Please select a city', value: '', disabled: true }].concat(citiesList)
                "
                @blur="v$.userForm.city.$touch()"></b-form-select>
              <b-form-invalid-feedback :state="validateState('city')">
                Please select a city
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group
              label="Password:"
              label-for="password-input"
              :state="validateState('password')">
              <b-input-group>
                <b-form-input
                  id="password-input"
                  placeholder="Enter your password"
                  v-model="userForm.password"
                  :type="hide ? 'password' : 'text'"
                  :state="validateState('password')"
                  @blur="v$.userForm.password.$touch()"></b-form-input>
                <b-input-group-append is-text>
                  <a @click="hide = !hide" class="color-unset">
                    <i class="fa" :class="{ 'fa-eye': !hide, 'fa-eye-slash': hide }"></i>
                  </a>
                </b-input-group-append>
              </b-input-group>
              <b-form-invalid-feedback>
                Password is required and must be at least 6 characters.
              </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6" lg="4">
            <b-form-group
              label="Confirm Password:"
              label-for="confirmPassword-input"
              :state="validateState('confirmPassword')">
              <b-input-group>
                <b-form-input
                  id="confirmPassword-input"
                  placeholder="Confirm your password"
                  v-model="userForm.confirmPassword"
                  :type="hide1 ? 'password' : 'text'"
                  :state="validateState('confirmPassword')"
                  @blur="v$.userForm.confirmPassword.$touch()"></b-form-input>
                <b-input-group-append is-text>
                  <a @click="hide1 = !hide1" class="color-unset">
                    <i class="fa" :class="{ 'fa-eye': !hide1, 'fa-eye-slash': hide1 }"></i>
                  </a>
                </b-input-group-append>
              </b-input-group>
              <b-form-invalid-feedback> Passwords do not match </b-form-invalid-feedback>
            </b-form-group>
          </b-col>
        </b-row>
        <div class="d-flex justify-content-center align-items-center mt-3">
          <b-button class="primaryBg mr-4" @click="onSubmit()" :disabled="v$.userForm.$invalid">
            Submit
          </b-button>
          <p class="m-0 font-weight-bold">Back to <router-link to="/login"> Login</router-link></p>
        </div>
      </b-form>
    </b-card>
  </div>
</template>

<script>
import { useVuelidate } from '@vuelidate/core';
import { email, maxLength, minLength, numeric, required, sameAs } from '@vuelidate/validators';
import { City, Country, State } from 'country-state-city';

export default {
  name: 'register',
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      userForm: {
        email: '',
        first_name: '',
        last_name: '',
        organization: '',
        contact: '',
        pincode: '',
        country: '',
        state: '',
        city: '',
        password: '',
        confirmPassword: '',
      },
      countriesList: [],
      statesList: [],
      citiesList: [],
      hide: true,
      hide1: true,
      isEmailValid: false,
    };
  },
  validations() {
    return {
      userForm: {
        email: {
          required,
          email,
        },
        first_name: {
          required,
        },
        last_name: {
          required,
        },
        organization: {
          required,
        },
        contact: {
          required,
          numeric,
          minLength: minLength(10),
        },
        pincode: {
          required,
          numeric,
          minLength: minLength(6),
          maxLength: maxLength(6),
        },
        country: {
          required,
        },
        state: {
          required,
        },
        city: {
          required,
        },
        password: {
          required,
          minLength: minLength(6),
        },
        confirmPassword: { required, sameAsPassword: sameAs(this.userForm.password) },
      },
    };
  },
  methods: {
    validateState(name) {
      const field = this.v$.userForm[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    fetchCountries() {
      const countriesList = Country.getAllCountries();
      this.countriesList = countriesList.map(country => ({
        text: country.name,
        value: country.isoCode,
      }));
    },
    getStatesList() {
      this.userForm.state = '';
      this.userForm.city = '';
      const statesList = State.getStatesOfCountry(this.userForm.country);
      this.statesList = statesList.map(state => ({
        text: state.name,
        value: state.isoCode,
      }));
    },
    getCitiesList() {
      this.userForm.city = '';
      const citiesList = City.getCitiesOfState(this.userForm.country, this.userForm.state);
      this.citiesList = citiesList.map(city => ({
        text: city.name,
        value: city.name,
      }));
    },
    onSubmit() {
      this.$store
        .dispatch('userStore/register', this.userForm)
        .then(response => {
          this.$router.push('/login');
          this.$root.$emit('showToast', {
            message: 'Please login to access the portal!',
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
        });
    },
    validateEmail() {
      this.$store
        .dispatch('userStore/isEmailUnique', this.userForm)
        .then(response => {
          this.isEmailValid = true;
          this.$root.$emit('showToast', {
            message: 'Please continue with the registration!',
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
        });
    },
  },
  mounted() {
    this.fetchCountries();
  },
};
</script>

<style scoped>
@media (max-width: 767px) {
  .inner_login .card {
    margin: 8px;
    max-width: calc(100% - 16px);
  }
  .text-primary i {
    font-size: 3rem;
  }
  .h4 {
    font-size: 1.25rem;
  }
  .form-group {
    margin-bottom: 16px;
  }
  .input-group-text {
    padding: 8px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
