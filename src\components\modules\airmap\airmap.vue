<template>
  <div class="map-layout">
    <div ref="myMap" class="w-100 h-100"></div>
    <div class="control-container-top">
      <div class="project-name">
        <marquee scrollamount="3">{{ project_name }}</marquee>
      </div>
      <div class="timeline">
        <timeline />
      </div>
      <div v-if="category === 'solar'">
        <VueToggles
          @click="changeStatus()"
          :value="project_type == 'SCPM'"
          width="100"
          height="35"
          checkedText="Progress"
          uncheckedText="Quality"
          checkedBg="#343a40"
          uncheckedBg="#343a40"
          fontSize="13" />
      </div>
    </div>
    <div class="layout-drop">
      <b-dropdown dropup text="Layouts">
        <b-dropdown-item-button v-for="(L1, ind) in this.cadUrls" :key="ind" @click.stop>
          <b-form-checkbox :id="'cad_' + L1.layerName" @change="checked(L1, ind)">
            <b>{{ L1.layerName }}</b>
          </b-form-checkbox>
        </b-dropdown-item-button>
      </b-dropdown>
    </div>
    <div class="control-container-bottom">
      <div class="weather">
        <div class="weather-modal" v-if="showWeather"><weather /></div>
        <b-button
          v-b-tooltip.hover.left
          title="Weather"
          class="btn-primary"
          @click="showWeather = !showWeather">
          <i class="fa fa-temperature-high"></i>
        </b-button>
      </div>
      <div class="satellite-view">
        <b-button
          class="btn-primary"
          @click="toggleMapLayer()"
          v-b-tooltip.hover.left
          title="Toggle Map Layer">
          <i class="fa fa-satellite" v-if="terrain"></i>
          <i class="fa fa-mountain-sun" v-else></i>
        </b-button>
      </div>
      <div class="full-screen">
        <b-button
          v-b-tooltip.hover.left
          title="Full Screen"
          class="btn-primary"
          @click="toggleFullscreen()">
          <i :class="fullscreenIcon"></i>
        </b-button>
      </div>
      <div class="reset-map">
        <b-button
          v-b-tooltip.hover.left
          title="Reset Map"
          class="btn-primary"
          @click="resetMapView()">
          <i class="fa fa-arrows-alt"></i>
        </b-button>
      </div>
      <!-- <div class="geo-location">
          <b-button
            v-b-tooltip.hover.left
            title="My Location"
            class="btn-primary"
            @click="getUserLocation()">
            <i class="fa fa-map-location-dot"></i>
          </b-button>
        </div> -->
      <div class="export-map">
        <b-button
          v-b-tooltip.hover.left
          title="Export Map"
          class="btn-primary"
          @click="exportMap()">
          <i class="fa fa-camera"></i>
        </b-button>
      </div>
    </div>
    <b-modal
      id="polygonModal"
      title="Range Definition"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <drawModal
        :polyDimensionProp="drawnPolygonDimension"
        :polygonArea="drawnPolygonArea"
        :aoiShapeType="aoiType"
        :selectedAoi="selectedAOI"
        :projectId="project_id"
        :currentDate="currentDate" />
    </b-modal>
    <b-modal
      @hidden="closeCadDetails"
      id="cadAlignModal"
      title="CAD Alignment"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <cadAlignModal :cadAlignProp="cadAlignData" />
    </b-modal>
    <b-modal
      size="lg"
      id="projectVideoModal"
      :title="videoModalTitle"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <videoModal
        :field_status_video_url="field_status_video_url"
        :time_lapse_video_url="time_lapse_video_url" />
    </b-modal>
    <b-modal
      size="lg"
      id="projectImageModal"
      :title="imageModalTitle"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <imageModal :outer_obliques="outer_obliques" :inner_obliques="inner_obliques" />
    </b-modal>
    <div id="print-template" class="print-only">
      <div class="print-header">
        <div class="header-content">
          <h6>Project Name: {{ project_name }}</h6>
          <h6>Flight Date: {{ currentDate }}</h6>
        </div>
      </div>
      <div class="print-map-container">
        <div id="print-map"></div>
      </div>
      <div class="print-footer">
        <p>Generated by © Datasee.AI at {{ dateFormat(new Date()) }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import '@/assets/plugins/leaflet-edgebuffer';
import omnivore from '@/assets/plugins/leaflet-omnivore';
import L from 'leaflet';
import 'leaflet-draw';
import 'leaflet-draw/dist/leaflet.draw.css';

import { formatCustomDate, formatProjectName } from '@/services/constant';
import { toRaw } from 'vue';
import { mapState } from 'vuex';

const timeline = () => import('@/components/modules/airmap/timeline.vue');
const weather = () => import('@/components/shared/weather.vue');
const cadAlignModal = () => import('@/components/modules/airmap/cadAlignModal.vue');
const drawModal = () => import('@/components/modules/airmap/drawModal.vue');
const videoModal = () => import('@/components/modules/home/<USER>');
const imageModal = () => import('@/components/modules/home/<USER>');

export default {
  name: 'airmap',
  components: {
    drawModal,
    cadAlignModal,
    videoModal,
    imageModal,
    weather,
    timeline,
  },
  data() {
    return {
      url: process.env.VUE_APP_TERRAIN_TILE_LAYER,
      satelliteUrl: process.env.VUE_APP_SATTELITE_TILE_LAYER,
      proxyUrl: `${process.env.VUE_APP_API_BASE_URL}project/proxy/?url=`,
      map: null,
      aoiLayer: null,
      markerLayer: null,
      cadLayer: null,
      kmlLayer: null,
      tileLayer: null,
      rgbLayer: null,
      options: {
        attributionControl: false,
        zoomControl: false,
        tapHold: false,
        minZoom: 2,
        maxZoom: 23,
        zoomSnap: 0.5,
        maxBoundsViscosity: 1.0,
        maxBounds: [
          [-90, -180],
          [90, 180],
        ],
      },
      tileOptions: {
        minZoom: 2,
        maxZoom: 23,
        edgeBufferTiles: 1,
      },
      showWeather: false,
      aoiLayerLabelMap: new Map(),
      createdAoi: null,
      drawnPolygonDimension: [],
      drawnPolygonArea: 0,
      aoiType: '',
      drawControl: null,
      polygonEnabler: null,
      terrain: false,
      isRealigned: false,
      cadLoaded: null,
      cadLayerLabelMap: new Map(),
      cadAlignData: { id: '', label: '', alignPoints: [] },
      selectedAOI: {},
      tempVar: '',
      videoModalTitle: '',
      imageModalTitle: '',
      fullscreen: false,
    };
  },
  computed: {
    ...mapState({
      currentDate: state => state.homeStore.projectData.currentDateProject.date,
      time_lapse_video_url: state =>
        state.homeStore.projectData.currentDateProject.time_lapse_video_url,
      field_status_video_url: state =>
        state.homeStore.projectData.currentDateProject.field_status_video_url,
      inner_obliques: state => state.homeStore.projectData.currentDateProject.inner_obliques,
      outer_obliques: state => state.homeStore.projectData.currentDateProject.outer_obliques,
      dsm: state => state.homeStore.projectData.currentDateProject.properties.ortho.DSM,
      rgb: state => state.homeStore.projectData.currentDateProject.properties.ortho.RGB,
      rgb_dev: state =>
        state.homeStore.projectData.currentDateProject.properties.deviation_ortho.RGB,
      cad: state => state.homeStore.projectData.currentDateProject.properties.CAD,
      project_type: state => state.homeStore.projectData.currentDateProject.type,
      zoom: state => state.homeStore.projectData.zoom_level,
      project_name: state => formatProjectName(state.homeStore.projectData.name),
      project_id: state => state.homeStore.projectData.id,
      category: state => state.homeStore.projectData.category,
      center: state => {
        const center = state.homeStore.projectData.center;
        if (!center) return [0, 0];
        const [lat, lng] = center.split(',').map(coord => parseFloat(coord.trim()));
        return !isNaN(lat) && !isNaN(lng) ? [lat, lng] : [0, 0];
      },
    }),
    cadUrls() {
      return this.cad
        ? Object.keys(this.cad).map(prop => ({
            layerName: prop,
            cadUrl: this.cad[prop] ? `${this.cad[prop]}{z}/{x}/{y}.png` : '',
          }))
        : [];
    },
    rgbUrl() {
      const { rgb_dev, rgb } = this;
      return rgb_dev && rgb_dev !== ''
        ? `${rgb_dev}{z}/{x}/{y}.png`
        : rgb && rgb !== ''
        ? `${rgb}{z}/{x}/{y}.png`
        : '';
    },
    fullscreenIcon() {
      return this.fullscreen
        ? 'fa fa-down-left-and-up-right-to-center'
        : 'fa fa-up-right-and-down-left-from-center';
    },
    dateFormat() {
      return originalDate => {
        return formatCustomDate(originalDate);
      };
    },
  },
  methods: {
    async exportMap() {
      try {
        this.$root.$emit('showToast', {
          message: 'Please wait while we are preparing for screenshot',
          title: 'Capturing Screenshot..',
          variant: 'info',
        });
        const printMapContainer = document.querySelector('#print-map');
        printMapContainer.innerHTML = '';

        // Create and append a new map container
        const printMapDiv = Object.assign(document.createElement('div'), {
          className: 'print-map-content',
          style: 'width: 100%; height: 100%;',
        });
        printMapContainer.appendChild(printMapDiv);

        // Clone the existing map and append it
        printMapDiv.appendChild(this.$refs.myMap.cloneNode(true));
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.$refs.myMap.invalidateSize?.();

        window.print();
        printMapContainer.innerHTML = '';
      } catch (error) {
        this.$root.$emit('showToast', {
          message: 'Failed to capture screenshot',
          title: 'Error',
          variant: 'danger',
        });
      }
    },
    toggleFullscreen() {
      try {
        const elem = document.documentElement;
        this.fullscreen = !this.fullscreen;

        if (this.fullscreen) {
          if (elem.requestFullscreen) elem.requestFullscreen();
          else if (elem.webkitRequestFullscreen) elem.webkitRequestFullscreen();
          else if (elem.msRequestFullscreen) elem.msRequestFullscreen();
        } else {
          if (document.exitFullscreen) document.exitFullscreen();
          else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
          else if (document.msExitFullscreen) document.msExitFullscreen();
        }
      } catch (error) {
        this.$root.$emit('showToast', {
          message: 'Try again later or contact support',
          title: error.message,
          variant: 'danger',
        });
      }
    },
    getUserLocation() {
      if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
          position => {
            const userLat = position.coords.latitude;
            const userLng = position.coords.longitude;
            const userMarker = L.marker([userLat, userLng]).addTo(toRaw(this.map));
            userMarker.bindPopup('Your Location').openPopup();
            this.map.flyTo([userLat, userLng], 20);
          },
          error => {
            this.$root.$emit('showToast', {
              message: `Try again later or contact support`,
              title: error.message,
              variant: 'danger',
            });
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          }
        );
      } else {
        this.$root.$emit('showToast', {
          message: 'Geolocation is not available in this browser.',
          title: 'Error Occured',
          variant: 'danger',
        });
      }
    },
    resetMapView() {
      this.map.flyTo(this.center, this.zoom);
    },
    initializeMap() {
      this.map = L.map(this.$refs.myMap, {
        ...this.options,
        center: this.center,
        zoom: this.zoom,
      });
      const rawMap = toRaw(this.map);

      L.control.scale({ position: 'bottomright', imperial: true, metric: true }).addTo(rawMap);
      L.control.zoom({ position: 'bottomright' }).addTo(rawMap);
      this.toggleMapLayer();
      this.updateRGBLayer();

      this.kmlLayer = new L.FeatureGroup().addTo(rawMap);
      this.cadLayer = new L.FeatureGroup().addTo(rawMap);
      this.markerLayer = new L.FeatureGroup().addTo(rawMap);
      this.aoiLayer = new L.FeatureGroup().addTo(rawMap);

      this.drawControl = new L.Control.Draw({
        position: 'topright',
        draw: {
          polyline: true,
          polygon: {
            allowIntersection: false,
            showArea: true,
          },
          rectangle: {
            allowIntersection: false,
            showArea: true,
          },
          circle: {
            allowIntersection: false,
            showArea: true,
          },
          marker: true,
          circlemarker: false,
        },
        edit: {
          featureGroup: this.aoiLayer,
          edit: true,
          remove: false,
        },
      });
      this.map.on('draw:created', this.aoiCreated);
      this.map.on('draw:edited', this.aoiEdited);
    },
    toggleMapLayer() {
      this.terrain = !this.terrain;
      if (this.tileLayer) this.map.removeLayer(this.tileLayer);

      this.tileLayer = L.tileLayer(this.terrain ? this.url : this.satelliteUrl, {
        ...this.tileOptions,
      });
      this.tileLayer.addTo(toRaw(this.map));

      if (this.rgbLayer) this.rgbLayer.setZIndex(1000);
    },
    updateRGBLayer() {
      if (this.rgbLayer) this.map.removeLayer(this.rgbLayer);

      this.rgbLayer = L.tileLayer(this.rgbUrl, {
        ...this.tileOptions,
      });
      this.rgbLayer.addTo(toRaw(this.map));
    },
    checked(item) {
      let summ = [];
      for (let item in this.cadUrls) {
        summ.push(this.cadUrls[item].layerName);
      }
      if (item.layerName == this.tempVar) {
        if (this.cadLoaded) this.map.removeLayer(this.cadLoaded);
        document.getElementById('cad_' + item.layerName).checked = false;
        this.tempVar = '';
      } else if (item.layerName != this.tempVar) {
        let indexed = summ.indexOf(item.layerName);
        if (indexed != -1) {
          summ.splice(indexed, 1);
        }
        for (let each of summ) {
          document.getElementById('cad_' + each).checked = false;
        }
        document.getElementById('cad_' + item.layerName).checked = true;
        if (this.cadLoaded != null) {
          this.map.removeLayer(this.cadLoaded);
        }
        this.cadLoaded = L.tileLayer(item.cadUrl, {
          maxZoom: 23,
        });
        this.map.addLayer(this.cadLoaded);
        this.tempVar = item.layerName;
      }
    },
    changeStatus() {
      this.popup_card_visibility = false;
      this.$store.dispatch('homeStore/switchProjectType');
      this.$root.$emit('removeKml');
      this.$root.$emit('closeSideBar');
    },
    renderGeoData(url, hex) {
      const kmlUrl = this.proxyUrl + encodeURIComponent(url);
      const customLayer = L.geoJson(null, {
        style: () => ({ color: hex, weight: 4 }),
        onEachFeature: (feature, layer) => {
          const desc = feature?.properties?.description || '';
          const descObj = {};
          const parser = new DOMParser();
          const markup = parser.parseFromString(desc, 'text/html');
          const tds = markup.getElementsByTagName('td');
          if (tds.length) {
            for (let i = 0; i < tds.length; i += 2) {
              const key = tds[i]?.textContent?.trim();
              const value = tds[i + 1]?.textContent?.trim();
              if (key && value) descObj[key] = value;
            }
          } else {
            desc
              .trim()
              .split('\n')
              .forEach(line => {
                const [key, value] = line.split(':').map(part => part.trim());
                if (key && value) descObj[key] = value;
              });
          }
          const popupContent = Object.entries(descObj)
            .map(([k, v]) => `<b>${k}</b>: ${v}`)
            .join('<br>');
          layer.bindPopup(popupContent || 'No description available.');
        },
      });
      const dataLayer = omnivore
        .kml(kmlUrl, null, customLayer)
        .on('ready', () => {
          if (dataLayer.getLayers().length) {
            const rawKmlLayer = toRaw(this.kmlLayer);
            rawKmlLayer.addLayer(dataLayer);
          }
        })
        .on('error', err => {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error loading KML file:', err);
          }
        });
    },
    createNewAOI(edit) {
      if (!edit) this.closeAOI();

      this.map.addControl(this.drawControl);
      this.polygonEnabler = new L.Draw.Polygon(this.map, this.drawControl.options.draw.polygon);
      this.$root.$emit('aoiCloseOnCreate');
    },
    aoiCreated(event) {
      this.processAoi(event.layerType, event.layer);
    },
    aoiEdited(event) {
      event.layers.eachLayer(layer => {
        this.processAoi(this.aoiType, layer);
      });
    },
    processAoi(type, layer) {
      this.aoiType = type;
      this.createdAoi = layer;
      const areaCalculators = {
        polygon: this.calculatePolygonArea,
        rectangle: this.calculatePolygonArea,
        circle: this.calculateCircleArea,
        polyline: this.calculatePolylineLength,
        marker: this.handleMarker,
      };

      if (areaCalculators[type]) {
        areaCalculators[type].call(this);
      } else {
        this.$root.$emit('showToast', {
          message: `Unknown layer type: ${type}`,
          title: 'Error Occurred!',
          variant: 'danger',
        });
      }
      this.$bvModal.show('polygonModal');
      this.map.removeControl(this.drawControl);
    },
    calculatePolygonArea() {
      this.drawnPolygonDimension = this.createdAoi.getLatLngs()[0];
      const areaInSquareMeters = L.GeometryUtil.geodesicArea(this.drawnPolygonDimension);
      this.drawnPolygonArea = (areaInSquareMeters / 10000).toFixed(2); // converting to hectares
    },
    calculateCircleArea() {
      this.drawnPolygonDimension = this.createdAoi.getLatLng();
      const radius = this.createdAoi.getRadius();
      const areaInSquareMeters = Math.PI * Math.pow(radius, 2);
      this.drawnPolygonArea = areaInSquareMeters.toFixed(2);
    },
    calculatePolylineLength() {
      this.drawnPolygonDimension = this.createdAoi.getLatLngs();
      let lengthInMeters = 0;
      for (let i = 1; i < this.drawnPolygonDimension.length; i++) {
        const latLng1 = this.drawnPolygonDimension[i - 1];
        const latLng2 = this.drawnPolygonDimension[i];
        lengthInMeters += latLng1.distanceTo(latLng2);
      }
      this.drawnPolygonArea = lengthInMeters.toFixed(2);
    },
    handleMarker() {
      this.drawnPolygonDimension = this.createdAoi.getLatLng();
      this.drawnPolygonArea = 0;
    },
    editCreatedAOI(aoi) {
      this.createNewAOI(true);
      this.selectedAOI = aoi;
      this.aoiType = this.selectedAOI.aoi_type;
      this.drawControl._toolbars.edit._modes.edit.handler.enable();
    },
    renderAOIItem(aoi) {
      if (this.aoiLayer) this.aoiLayer.clearLayers();
      if (this.markerLayer) this.markerLayer.clearLayers();
      this.selectedAOI = {};
      const aoi_key = `${aoi.label}-${aoi.id}`;
      let existingAOI;
      if (
        aoi.aoi_type === 'polygon' ||
        aoi.aoi_type === 'rectangle' ||
        aoi.aoi_type === 'polyline'
      ) {
        existingAOI = L.polygon(aoi.polygon, {
          color: '#ff7800',
          weight: 1,
        });
        const edges = existingAOI.getLatLngs()[0];

        const markers = L.layerGroup();
        edges.forEach(edge => {
          const multiLineContent = `
          <b>Latitude</b>: ${edge.lat.toFixed(6)}<br/>
          <b>Longitude</b>: ${edge.lng.toFixed(6)}<br/>
        `;
          const marker = L.marker([edge.lat, edge.lng]).bindPopup(multiLineContent).openPopup();
          markers.addLayer(toRaw(marker));
        });

        this.markerLayer.addLayer(toRaw(markers));
        this.map.fitBounds(existingAOI.getBounds());
      } else {
        const circleRadius = Math.sqrt(aoi.area / Math.PI);
        const circleCenter = [aoi.polygon.lat, aoi.polygon.lng];

        existingAOI = L.circle(circleCenter, {
          color: '#ff7800',
          radius: circleRadius,
        });
        const popupContent = `
          <b>Latitude</b>: ${circleCenter[0].toFixed(6)}<br/>
          <b>Longitude</b>: ${circleCenter[1].toFixed(6)}<br/>
        `;
        const marker = L.marker(circleCenter).bindPopup(popupContent).openPopup();

        this.markerLayer.addLayer(toRaw(marker));
        this.map.setView([aoi.polygon.lat, aoi.polygon.lng], 16);
      }
      this.aoiLayerLabelMap.set(aoi_key, existingAOI);
      this.aoiLayer.addLayer(toRaw(existingAOI));
    },
    removeAoi(aoiLabel) {
      const deletedAoi = this.aoiLayerLabelMap.get(aoiLabel);
      if (!deletedAoi) return;

      this.aoiLayer.removeLayer(deletedAoi);
      this.markerLayer?.clearLayers();
      this.aoiLayerLabelMap.delete(aoiLabel);
    },
    closeAOI() {
      this.aoiLayer?.clearLayers();
      this.markerLayer?.clearLayers();
      this.drawControl && this.map.removeControl(this.drawControl);

      this.polygonEnabler?.disable();
      this.aoiLayerLabelMap.clear();
      this.map?.invalidateSize();
    },
    downloadAOI(aoi) {
      const label = aoi.label.trim();
      const polygon = Array.isArray(aoi.polygon) ? aoi.polygon : [aoi.polygon];
      const coordinates = polygon.map(point => `${point.lng},${point.lat}`).join('\n');

      const kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <Placemark>
      <name>${label}</name>
      <description>${aoi.description}</description>
      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>
              ${coordinates}
            </coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
    </Placemark>
  </Document>
</kml>`;

      const blob = new Blob([kml], { type: 'application/vnd.google-earth.kml+xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${label}.kml`;
      a.click();
      URL.revokeObjectURL(url);
    },
    renderCad(cad) {
      if (!this.cadLayerLabelMap.has(cad.label)) {
        let cadLayer;
        let cadLayers = [];
        let cadUrlArray = [];
        if (cad.realign_cad_kml.length > 0) {
          cadUrlArray = cad.realign_cad_kml;
          this.isRealigned = true;
        } else {
          cadUrlArray = cad.uploaded_cad_kml;
          this.isRealigned = false;
        }
        cadUrlArray.forEach(url => {
          // Improve below function call in order to display CAD properly.
          cadLayer = this.renderGeoData(url, this.cadLayer, cad.color);
          cadLayers.push(cadLayer);
        });
        this.cadId = cad.id;
        this.cadLayerLabelMap.set(cad.label, cadLayers);
      } else {
        let cadLayers = this.cadLayerLabelMap.get(cad.label);
        const bounds = cadLayers[0].getBounds();
        this.map.fitBounds(bounds);
      }
      this.cadAlignData.label = cad.label;
      this.cadAlignData.id = cad.id;
    },
    reAlignCad(cadItem) {
      this.cadAlignData.label = cadItem.label;
      this.cadAlignData.id = cadItem.id;
      L.DomUtil.addClass(this.map._container, 'crosshair-cursor-enabled');
      this.map.on('click', mapClick => {
        this.addCadAlignPoints(mapClick.latlng);
      });
    },
    revertRealign(cadItem) {
      this.$store.dispatch('airmapStore/revertRealignApi', cadItem.id).then(response => {
        if (response === true) {
          let originalCad = {
            creation_date: cadItem.creation_date,
            date: cadItem.date,
            id: cadItem.id,
            label: cadItem.label,
            location_id: cadItem.location_id,
            project: cadItem.project,
            uploaded_cad_kml: cadItem.uploaded_cad_kml,
            realign_cad_kml: [],
            realigned_by: null,
            realign_date: null,
          };
          this.cadLayer.clearLayers();
          this.cadLayerLabelMap.clear();
          this.renderCad(originalCad);
          this.$root.$emit('cadAlignReverted', originalCad);
        }
      });
    },
    addCadAlignPoints(latlng) {
      const newMarker = new L.marker(latlng);
      this.cadLayer.addLayer(newMarker);
      this.cadAlignData.alignPoints.push(latlng);

      if (this.cadAlignData.alignPoints.length >= 8) {
        this.$bvModal.show('cadAlignModal');
      }
    },
    removeCad(cadLabel) {
      if (this.cadLayerLabelMap.has(cadLabel)) {
        this.cadLayer.clearLayers();
        this.measureLayerLabelMap.delete(cadLabel);
        this.$root.$emit('showToast', {
          message: `${cadLabel} deleted successfully`,
          title: 'Deleted!',
          variant: 'success',
        });
      }
    },
    closeCadDetails() {
      L.DomUtil.removeClass(this.map._container, 'crosshair-cursor-enabled');
      this.cadLayer ? this.cadLayer.clearLayers() : '';
      this.cadAlignData.alignPoints = [];
      this.cadAlignData.label = '';
      this.map.off('click');
    },
  },
  mounted() {
    this.$nextTick(() => {
      L.Marker.prototype._animateZoom = function (opt) {
        if (!this._map) return;
        const pos = this._map._latLngToNewLayerPoint(this._latlng, opt.zoom, opt.center).round();
        this._setPos(pos);
      };

      L.Popup.prototype._animateZoom = function (opt) {
        if (!this._map) return;
        const pos = this._map._latLngToNewLayerPoint(this._latlng, opt.zoom, opt.center).round();
        const anchor = this._getAnchor();
        L.DomUtil.setPosition(this._container, pos.add(anchor));
      };

      this.initializeMap();
      const dropdownMenu = this.$el.querySelector('.dropdown-menu');
      if (dropdownMenu) dropdownMenu.addEventListener('wheel', e => e.stopPropagation());
    });
    this.$root.$on('initializeMap', () => {
      if (this.map && this.tileLayer && this.rgbLayer) this.updateRGBLayer();
    });
    this.$root.$on('kmlClicked', kml => {
      this.renderGeoData(kml.url, kml.hex, kml.is_polyline);
    });
    this.$root.$on('removeKml', () => {
      if (this.kmlLayer) this.kmlLayer.clearLayers();
    });
    this.$root.$on('createNewAoi', () => {
      this.createNewAOI(false);
    });
    this.$root.$on('aoiEdited', aoi => {
      this.editCreatedAOI(aoi);
    });
    this.$root.$on('aoiItemClicked', aoi => {
      this.renderAOIItem(aoi);
    });
    this.$root.$on('aoiClosed', () => {
      this.closeAOI();
    });
    this.$root.$on('aoiDownload', aoi => {
      this.downloadAOI(aoi);
    });
    this.$root.$on('aoiDeleted', aoiLabel => {
      this.removeAoi(aoiLabel);
    });
    this.$root.$on('cadClicked', cad => {
      this.cadLayer.clearLayers();
      this.cadLayerLabelMap.clear();
      this.renderCad(cad);
    });
    this.$root.$on('cadSideNavClosed', () => {
      this.closeCadDetails();
    });
    this.$root.$on('cadDeleted', cadLabel => {
      this.removeCad(cadLabel);
    });
    this.$root.$on('reAlignClicked', cadItem => {
      this.reAlignCad(cadItem);
    });
    this.$root.$on('revertReAlignClicked', cadItem => {
      this.revertRealign(cadItem);
    });
    this.$root.$on('openVideo', () => {
      this.videoModalTitle = `Current Field Status / Time Lapse View for ${this.currentDate}`;
      const hasVideo = Boolean(this.time_lapse_video_url) || Boolean(this.field_status_video_url);
      if (hasVideo) {
        this.$bvModal.show('projectVideoModal');
      } else {
        this.$root.$emit('showToast', {
          message: 'For the current date selected.',
          title: 'Video Not Available!!',
          variant: 'info',
        });
      }
    });
    this.$root.$on('openImages', () => {
      this.imageModalTitle = `Outer / Inner Obliques Images for ${this.currentDate}`;
      const hasInnerImages = Object.values(this.inner_obliques || {}).some(
        arr => Array.isArray(arr) && arr.length > 0
      );
      const hasOuterImages = Object.values(this.outer_obliques || {}).some(
        arr => Array.isArray(arr) && arr.length > 0
      );

      if (hasInnerImages || hasOuterImages) {
        this.$bvModal.show('projectImageModal');
      } else {
        this.$root.$emit('showToast', {
          message: 'For the current date selected.',
          title: 'Images Not Available!!',
          variant: 'info',
        });
      }
    });
  },
  beforeUnmount() {
    this.$root.$off('toggleMarkers');
    this.$root.$off('openImages', this.handleOpenImages);
    this.$root.$off('openVideo', this.handleOpenVideo);
  },
};
</script>

<style scoped>
.map-layout {
  height: 100vh;
}
.control-container-bottom {
  position: fixed;
  z-index: 1000;
  bottom: 0rem;
  right: 3rem;
}
.control-container-top {
  position: fixed;
  z-index: 1000;
  right: 0.5rem;
  top: 0.5rem;
}
.project-name {
  font-weight: 600;
  width: 10rem;
  right: 16rem;
  position: absolute;
  font-size: 1.3rem;
}
.timeline {
  position: absolute;
  right: 7rem;
}
.full-screen {
  position: absolute;
  bottom: 8.5rem;
}
.reset-map {
  position: absolute;
  bottom: 0.5rem;
}
.satellite-view {
  position: absolute;
  bottom: 11rem;
}
.weather {
  position: absolute;
  bottom: 13.5rem;
}
.weather-modal {
  position: fixed;
  right: 3.5rem;
  top: 8rem;
}
.export-map {
  position: absolute;
  bottom: 16rem;
}
.geo-location {
  position: absolute;
  bottom: 18.5rem;
}
.checkbox-item {
  text-align: center;
  padding: 0px;
}
.layout-drop {
  position: absolute;
  right: 12rem;
  bottom: 5px;
  z-index: 1000;
}
.pop-up-content {
  padding: 5px;
  font-size: 12px;
}
.closeIcon {
  padding: 10px;
  transition: all 0.2s;
}
.leaflet-container.crosshair-cursor-enabled {
  cursor: crosshair;
}
</style>
