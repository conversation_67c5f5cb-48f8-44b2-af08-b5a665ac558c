<template>
  <div>
    <b-list-group>
      <b-list-group-item v-for="(itemL1, keyL1, indexL1) in data" :key="indexL1">
        <div class="sideBarList">
          <div class="d-flex flex-row justify-content-between">
            <span>
              <i
                @click="lightning(keyL1, itemL1)"
                :id="'eye_' + keyL1"
                :class="`fas fa-eye-slash font_light mr-2`"></i>
            </span>
            <span v-b-toggle="`summary${indexL1}L1`" class="text-capitalize">{{ keyL1 }}</span>
            <span><i class="fas fa-caret-down"></i></span>
          </div>
          <b-collapse v-if="Object.keys(itemL1).length >= 1" :id="`summary${indexL1}L1`">
            <div
              class="subItem1 pointer"
              id="subItem1"
              v-for="(itemL2, keyL2, indexL2) in itemL1"
              :key="`${keyL2}-${indexL2}`"
              :class="{ active: clickedDetails === itemL2 }">
              <div v-b-toggle="`${indexL1}${indexL2}L2`">
                <div
                  class="sublink d-flex justify-content-between align-items-center"
                  @click="renderDetails(keyL1, keyL2, itemL2)">
                  <span
                    v-b-tooltip.hover.left.html="
                      `<b>Actual</b>: ${itemL2.Actual} <br /> <b>Planned</b>: ${itemL2.Total}`
                    ">
                    {{ keyL2 }}
                  </span>
                  <span class="d-flex align-items-center ml-3">
                    {{ itemL2.Actual }}
                    <span :style="{ color: itemL2.color }" class="fas fa-circle float-right ml-3">
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </b-collapse>
        </div>
      </b-list-group-item>
    </b-list-group>
    <div>
      <div v-if="clickedDetailsVisible" class="sideBarDetailsContainer">
        <div class="clickedHeader d-flex justify-content-between px-2">
          <span>{{ kmlheading }}</span>
          <span>
            <i class="icon fa fa-times" @click="closeDetails()"></i>
          </span>
        </div>
        <div class="clickedDetails d-flex flex-row align-items-center justify-content-between">
          <div class="w-75">
            <b>Actual:</b> {{ clickedDetails.Actual }} <br />
            <b>Planned:</b> {{ clickedDetails.Total }}
          </div>
          <div class="w-25">
            <apexchart
              type="radialBar"
              height="100"
              :options="options"
              :series="series"></apexchart>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'summarySideNav',
  props: ['data'],
  data() {
    return {
      clickedDetailsVisible: false,
      clickedDetails: {},
      kmlheading: '',
      total_percentage: Number,
      temp1: '',
      temp2: '',
    };
  },
  computed: {
    kmlBaseUrl() {
      return this.$store.state.homeStore.projectData.currentDateProject.properties.kml ?? '';
    },
    currentDate() {
      const currentDate = this.$store.state.airmapStore.currentDate;
      return currentDate ? currentDate : '';
    },
    series() {
      return [parseInt(this.total_percentage.toFixed())];
    },
    options() {
      return {
        plotOptions: {
          radialBar: {
            dataLabels: {
              show: true,
              name: {
                show: false,
              },
              value: {
                show: true,
                offsetY: 8,
              },
            },
            hollow: {
              size: '40%',
            },
          },
        },
      };
    },
  },
  methods: {
    lightning(keyL1, i) {
      this.$root.$emit('removeKml');
      const eyeIcon = document.getElementById('eye_' + keyL1);
      if (keyL1 === this.temp2) {
        eyeIcon.className = 'far fa-eye-slash';
        this.temp2 = '';
      } else {
        const eyeElements = document.querySelectorAll('[id^="eye_"]');
        eyeElements.forEach(element => {
          element.className = 'far fa-eye-slash';
        });
        eyeIcon.className = 'far fa-eye';
        for (const n in i) {
          const kml = i[n].kml || ' ';
          const color = i[n].color || 'rgb(26, 25, 23)';
          const rgb = color
            .replace(/[^\d,]/g, '')
            .split(',')
            .map(Number);
          const hex_color = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
          for (const p in kml) {
            this.$root.$emit('kmlClicked', {
              url: `${this.kmlBaseUrl}summary/${kml[p]}`,
              hex: hex_color,
              is_polyline: true,
            });
          }
        }
        this.temp2 = keyL1;
      }
    },
    renderDetails(keyL2, keyL3, value) {
      if (keyL3 !== this.temp1) {
        document.getElementById('subItem1').style.fontWeight = '200';
      }
      this.$root.$emit('removeKml');
      this.clickedDetailsVisible = true;
      this.clickedDetails = value;
      this.kmlheading = keyL3;
      if (this.clickedDetails.Total === 'NA') {
        this.total_percentage = 0;
      } else {
        this.total_percentage = (this.clickedDetails.Actual / this.clickedDetails.Total) * 100;
      }
      if (this.clickedDetails.Actual !== '0.0') {
        const rgb = value.color
          .replace(/[^\d,]/g, '')
          .split(',')
          .map(Number);
        const hex_color = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
        if (value && value.kml) {
          for (const kml of value.kml) {
            this.$root.$emit('kmlClicked', {
              url: `${this.kmlBaseUrl}summary/${kml}`,
              hex: hex_color,
              is_polyline: true,
            });
            if (value.blankarea_kml) {
              for (const kml of value.blankarea_kml) {
                this.$root.$emit('kmlClicked', {
                  url: `${this.kmlBaseUrl}summary/${kml}`,
                  hex: value?.blankarea_color || '#696969',
                  is_polyline: true,
                });
              }
            }
          }
        }
      }
      this.temp1 = keyL3;
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('removeKml');
    },
  },
  unmounted() {
    this.$root.$emit('removeKml');
  },
};
</script>

<style scoped></style>
