<template>
  <div>
    <navbar />
    <div class="container text-center p-3">
      <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-2">
        <div class="d-flex align-items-center mb-3">
          <label class="mr-2 mb-0">Items per page:</label>
          <b-form-select
            v-model="itemsPerPage"
            :options="perPageOptions"
            class="w-auto"></b-form-select>
        </div>
        <b-form-group class="flex-fill mx-md-2 mb-3">
          <b-input-group>
            <b-form-input v-model="searchQuery" placeholder="Search.." />
            <b-input-group-append>
              <b-button :disabled="!searchQuery" @click="clearSearch">Clear</b-button>
            </b-input-group-append>
          </b-input-group>
        </b-form-group>
        <p class="mb-3 font-weight-bold text-nowrap">
          Showing {{ filteredItems.length }} of {{ manageUsers.length }} rows
        </p>
      </div>
      <b-table
        bordered
        striped
        hover
        responsive="sm"
        show-empty
        sticky-header="400px"
        no-border-collapse
        head-variant="dark"
        :items="paginatedItems"
        :fields="fields"
        @sort-changed="handleSort">
        <template #emptyfiltered>
          <h6><b>Search data not found.</b></h6>
        </template>
        <template #cell(name)="data">
          <b class="text-capitalize">{{ data.value }}</b>
        </template>
        <template #cell(enabled)="data">
          <VueToggles
            @click="changeAccess(data.item)"
            :value="data.value"
            height="25"
            checkedText="Yes"
            uncheckedText="No"
            checkedBg="darkcyan"
            uncheckedBg="#343a40"
            fontSize="14"
            fontWeight="800" />
        </template>
        <template #cell(role)="data">
          <span class="text-capitalize">{{ data.value || '---' }}</span>
        </template>
        <template #cell(created_on)="data">
          {{ dateFormat(data.value) }}
        </template>
        <template #cell(edit)="data">
          <div class="mx-2 pointer" v-b-modal.editData @click="setUserModalData(data.item)">
            <i class="fas fa-edit fa-2x"></i>
          </div>
        </template>
      </b-table>
      <b-pagination
        v-if="filteredItems.length > itemsPerPage"
        v-model="currentPage"
        :total-rows="filteredItems.length"
        :per-page="itemsPerPage"
        first-text="First"
        prev-text="Prev"
        next-text="Next"
        last-text="Last"
        align="center"
        pills
        :limit="2"
        class="my-3" />
    </div>
    <b-modal
      ref="modalRef"
      id="editData"
      size="lg"
      title="User Data Edit"
      :hide-footer="true"
      no-close-on-esc
      no-close-on-backdrop
      centered
      v-if="user">
      <b-form>
        <b-row>
          <b-col cols="12" md="6">
            <b-form-group id="input-group-2" label="Full Name:" label-for="input-2">
              <b-form-input class="w-100" id="input-2" v-model="user.name" disabled></b-form-input>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6">
            <b-form-group id="input-group-1" label="Email address:" label-for="input-1">
              <b-form-input class="w-100" id="input-1" v-model="user.email" disabled></b-form-input>
            </b-form-group>
          </b-col>
        </b-row>
        <b-row>
          <b-col cols="12" md="6">
            <b-form-group id="input-group-3" label="Role:" label-for="input-3">
              <b-form-select
                id="input-3"
                v-model="user.role"
                :options="role_options"
                required></b-form-select>
            </b-form-group>
          </b-col>
          <b-col cols="12" md="6">
            <b-form-group id="input-group-4" label="Created On:" label-for="input-4">
              <b-form-input class="w-100" id="input-4" v-model="getDate" disabled></b-form-input>
            </b-form-group>
          </b-col>
        </b-row>
        <div class="text-center">
          <b-button class="primaryBg login_btn mx-2" @click="updateRoleAccess()">Submit</b-button>
          <b-button class="login_btn mx-2" @click="$refs.modalRef.hide()">Close</b-button>
        </div>
      </b-form>
    </b-modal>
  </div>
</template>

<script>
import { formatCustomDate } from '@/services/constant';
const navbar = () => import('@/components/shared/navbar.vue');

export default {
  name: 'manageUsers',
  components: {
    navbar,
  },
  data() {
    return {
      user: null,
      old_role: null,
      searchQuery: '',
      sortBy: '',
      sortDesc: false,
      currentPage: 1,
      itemsPerPage: 5,
      perPageOptions: [5, 10, 15, 20],
      fields: [
        { key: 'name', label: 'Full Name', sortable: true },
        { key: 'email', sortable: true },
        { key: 'role', sortable: true },
        { key: 'enabled', sortable: true },
        { key: 'created_on', sortable: true },
        'edit',
      ],
      role_options: [],
    };
  },
  computed: {
    manageUsers() {
      return this.$store.state.userStore.manageUsers;
    },
    getDate() {
      return formatCustomDate(this.user.created_on);
    },
    dateFormat() {
      return originalDate => {
        return formatCustomDate(originalDate);
      };
    },
    getRoles() {
      return this.$store.state.userStore.roles;
    },
    filteredItems() {
      let filtered = this.manageUsers;
      // Apply search filter across all fields
      if (this.searchQuery) {
        const search = this.searchQuery.toLowerCase().trim();
        filtered = filtered.filter(item =>
          Object.values(item).some(value => value?.toString().toLowerCase().includes(search))
        );
      }
      // Apply sorting
      if (this.sortBy) {
        const isDescending = this.sortDesc;
        filtered = filtered.slice().sort((a, b) => {
          const aVal = a[this.sortBy] ?? '';
          const bVal = b[this.sortBy] ?? '';
          const aStr = typeof aVal === 'string' ? aVal.toLowerCase() : String(aVal).toLowerCase();
          const bStr = typeof bVal === 'string' ? bVal.toLowerCase() : String(bVal).toLowerCase();
          return isDescending
            ? bStr.localeCompare(aStr, undefined, { numeric: true })
            : aStr.localeCompare(bStr, undefined, { numeric: true });
        });
      }
      return filtered;
    },
    paginatedItems() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredItems.slice(start, end);
    },
  },
  methods: {
    clearSearch() {
      this.searchQuery = '';
      this.currentPage = 1;
    },
    resetPage() {
      this.currentPage = 1;
    },
    handleSort(ctx) {
      this.sortBy = ctx.sortBy;
      this.sortDesc = ctx.sortDesc;
    },
    initializeRoleOptions() {
      this.role_options = [
        { text: 'Select One', value: null, disabled: true },
        ...this.getRoles.map(role => ({
          value: role,
          text: role.toUpperCase(),
        })),
      ];
    },
    getUsers(dataUpdate) {
      this.$store.dispatch('userStore/getManageUsers', dataUpdate).catch(error => {
        this.$root.$emit('showToast', {
          title: error.message,
          message: 'There has been a problem in fetching users data',
          variant: 'danger',
        });
      });
    },
    setUserModalData(user) {
      this.old_role = user.role;
      this.user = user;
    },
    changeAccess(user) {
      const data = {
        id: user.id,
        enabled: !user.enabled,
        email: user.email,
      };
      this.$store
        .dispatch('userStore/updateUserActiveAccess', data)
        .then(response => {
          this.$root.$emit('showToast', {
            message: 'Data updated successfully',
            variant: 'success',
            title: response.message,
          });
          this.getUsers(true);
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: 'Error in updating data',
            variant: 'danger',
            title: error.message,
          });
        });
    },
    updateRoleAccess() {
      const data = {
        id: this.user.id,
        email: this.user.email,
        role: this.user.role,
        old_role: this.old_role,
      };
      if (this.user.role !== this.old_role) {
        this.$store
          .dispatch('userStore/updateRoleAccess', data)
          .then(response => {
            this.$bvModal.hide('editData');
            this.$root.$emit('showToast', {
              message: 'Data updated successfully',
              variant: 'success',
              title: response.message,
            });
            this.getUsers(true);
          })
          .catch(error => {
            this.$root.$emit('showToast', {
              message: 'Error in updating data',
              variant: 'danger',
              title: error.message,
            });
          });
      } else {
        this.$root.$emit('showToast', {
          variant: 'warning',
          message: 'Please check again',
          title: 'Role already assigned',
        });
      }
    },
  },
  mounted() {
    this.initializeRoleOptions();
    this.getUsers(false);
  },
  watch: {
    searchQuery: 'resetPage',
    itemsPerPage: 'resetPage',
  },
};
</script>

<style scoped>
@media (max-width: 767px) {
  .table {
    font-size: 11px;
  }
  .table td,
  .table th {
    padding: 4px 6px;
    vertical-align: middle;
  }
  .input-group {
    margin-bottom: 12px;
  }
  .modal-dialog {
    margin: 8px;
    max-width: calc(100% - 16px);
  }
  .fa-edit {
    font-size: 1.5rem !important;
  }
  .vue-toggles {
    transform: scale(0.9);
  }
}
</style>
