<template>
  <div class="inner_login">
    <b-card class="shadow mx-auto w-100 max-width-500">
      <div class="text-center text-primary">
        <i class="fa fa-key fa-4x"></i>
        <p class="my-2 h4 font-weight-bold">Account Recovery</p>
      </div>
      <b-form class="w-100 mt-3">
        <label for="password-input" class="mt-3">Enter your new password:</label>
        <b-input-group>
          <b-form-input
            id="password-input"
            placeholder="Enter your new password"
            v-model="passwordForm.password"
            :type="hide ? 'password' : 'text'"
            @blur="v$.passwordForm.password.$touch()"></b-form-input>
          <b-input-group-append is-text>
            <a @click="hide = !hide" class="color-unset">
              <i class="fa" :class="{ 'fa-eye': !hide, 'fa-eye-slash': hide }"></i>
            </a>
          </b-input-group-append>
          <b-form-invalid-feedback :state="validateState('password')">
            Password is required and must be at least 6 characters.
          </b-form-invalid-feedback>
        </b-input-group>
        <label for="confirmPassword-input" class="mt-3">Confirm your new password:</label>
        <b-input-group>
          <b-form-input
            id="confirmPassword-input"
            placeholder="Confirm your new password"
            v-model="passwordForm.confirmPassword"
            :type="hide1 ? 'password' : 'text'"
            @blur="v$.passwordForm.confirmPassword.$touch()"></b-form-input>
          <b-input-group-append is-text>
            <a @click="hide1 = !hide1" class="color-unset">
              <i class="fa" :class="{ 'fa-eye': !hide1, 'fa-eye-slash': hide1 }"></i>
            </a>
          </b-input-group-append>
          <b-form-invalid-feedback :state="validateState('confirmPassword')">
            Passwords do not match.
          </b-form-invalid-feedback>
        </b-input-group>
        <div class="text-center mt-3">
          <b-button class="primaryBg" @click="onSubmit()" :disabled="v$.passwordForm.$invalid">
            Submit
          </b-button>
        </div>
      </b-form>
    </b-card>
  </div>
</template>

<script>
import { useVuelidate } from '@vuelidate/core';
import { minLength, required, sameAs } from '@vuelidate/validators';

export default {
  name: 'recovery',
  setup() {
    return { v$: useVuelidate() };
  },
  props: ['uid', 'token'],
  data() {
    return {
      passwordForm: {
        password: '',
        confirmPassword: '',
      },
      hide: true,
      hide1: true,
    };
  },
  validations() {
    return {
      passwordForm: {
        password: {
          required,
          minLength: minLength(6),
        },
        confirmPassword: {
          required,
          sameAsPassword: sameAs(this.passwordForm.password),
        },
      },
    };
  },
  methods: {
    validateState(name) {
      const field = this.v$.passwordForm[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    onSubmit() {
      const data = {
        password: this.passwordForm.password,
        type: 'reset',
        id: this.uid,
        token: this.token,
      };
      this.$store
        .dispatch('userStore/resetPassword', data)
        .then(response => {
          this.$router.push('/login');
          this.$root.$emit('showToast', {
            message: `Password Changed Successfully`,
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
        });
    },
  },
};
</script>

<style scoped></style>
